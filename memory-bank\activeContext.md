# Active Context: Used Car Price Prediction Platform Website Development

## Current Status: Website Development Kick-off
Project initiated based on the "Website Development Plan: Used Car Price Prediction Platform".
All work will be done inside the `used_cars_saas` directory.

### Existing Assets for this Website Development Project:
1.  **Pre-trained Models**:
    *   `catboost_main_model.cbm`
    *   `catboost_error_model.cbm`
2.  **Core Planning Document**:
    *   `Website Plan for Car Prediction_.pdf` (This contains the detailed plan we are following)
3.  **Dataset (for reference, default values, and dropdowns)**:
    *   `cars_df.parquet` (or similar, to extract make/model lists and median/mode for defaults)

## Current Focus: Phase 0 - Final Preparations & Phase 1 Initiation

### Immediate Actions & Decisions Required (User Input Needed for some):
1.  **Finalize Technology Choices (User to Confirm)**:
    *   **Frontend Framework**: React or Vue.js?
    *   **JavaScript Visualization Libraries**: Plotly.js, ECharts, D3.js, or a combination?
2.  **Data Collation & Preparation (Part of Phase 1 Prep)**:
    *   Extract unique `make_name` values for the Make dropdown.
    *   Extract unique `model_name` values for each `make_name` for the dynamic Model dropdown.
    *   Compile the comprehensive list of all ~37 features the CatBoost models require (based on the original notebook `used_cars_project copy 3.ipynb` and the `Website Development Plan`).
    *   For each of the ~32 features *not* directly provided by the user on the 'Model Predictor' page, determine and document its default value (median for numerical, mode for categorical) by analyzing `cars_df.parquet` or the EDA in `used_cars_project copy 3.ipynb` (as referenced by Table 2 in the Website Plan).
    *   Define the consistent `listed_year` to be used for `car_age` calculation (e.g., 2021 as per plan, or current year). 

## Next Steps (Initiating Phase 1 of Website Plan):
1.  **Setup `used_cars_saas` Directory Structure**: Create subdirectories for `backend` and `frontend`.
2.  **Backend Development (FastAPI) - Phase 1**: Begin work on:
    *   FastAPI project setup within `used_cars_saas/backend/`.
    *   `/predict` API endpoint.
    *   Model loading logic for `catboost_main_model.cbm` and `catboost_error_model.cbm`.
    *   Implementation of feature processing (5 user inputs + ~32 defaults, `car_age`, `mileage_per_age`).
    *   Two-stage prediction logic.
    *   API for dropdown data (make/model lists).

## Blockers/Risks:
-   Accuracy of default value extraction for the ~32 supplementary features is critical.
-   Ensuring the exact replication of the feature engineering pipeline from the notebook for the 5 user inputs and their combination with defaults.
-   Performance of loading and running the large CatBoost models in the FastAPI backend.
-   Selection of appropriate visualization libraries that balance aesthetics (animated/3D) with performance and development complexity. 