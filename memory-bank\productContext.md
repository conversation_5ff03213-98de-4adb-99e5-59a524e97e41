# Product Context: Used Car Price Prediction Platform Website

## Problem Statement
The used car market often suffers from price opacity. Buyers and sellers need a reliable, accessible tool to understand fair market value and the factors influencing it. This project aims to build a website that not only provides accurate price predictions using a sophisticated ML model but also educates users through engaging data visualizations.

## Target Users (as per Website Plan)
- Car dealers and automotive professionals
- Individual car buyers and sellers
- Automotive market analysts
- Users interested in data science project showcases

## Solution Overview: The Website

### Core Value Proposition
"An interactive and engaging web platform for predicting used car prices with high accuracy and exploring underlying market data through dynamic visualizations."

### Website Components (as per Website Plan)

#### 1. Home Page
- **Objectives**:
    - Provide a concise introduction to the used car price prediction project.
    - Engage users with compelling, data-driven visualizations (animated or 3D preferred by user).
    - Clearly direct users to the 'Model Predictor' tab.
- **Content**:
    - Brief summary of the project (goal: predict used car prices from large dataset).
    - Prominent call-to-action/link to 'Model Predictor' page.
- **Engaging Visualizations (Examples from Plan)**:
    - **Interactive Price Distribution Explorer**: Filterable by make, year, body type; animated transitions.
    - **3D Scatter Plot (Price vs. Mileage vs. Car Age)**: Rotatable, zoomable, points colored by make/body type (e.g., using Plotly.js).
    - **Animated Feature Importance Display**: Dynamic bar chart of top N features influencing price.
    - **Dynamic Correlation Explorer**: Interactive heatmap or selection-based display of feature correlations.
    - *Technical Note*: Requires advanced JS libraries (Plotly.js, ECharts, D3.js), data aggregation/sampling for performance.

#### 2. 'Model Predictor' Page
- **Objective**: Allow users to obtain price predictions for specific vehicles.
- **User Interface (UI)**:
    - Clear instructions.
    - Input Fields (5 primary user inputs):
        - **Make**: Dropdown (from predefined list of 100 unique values).
        - **Model**: Dynamic Dropdown (filtered by selected Make, from 1396 unique values).
        - **Number of Cylinders**: Dropdown/Numeric (from unique values: 2, 3, 4, 5, 6, 8, 10, 12, 16).
        - **Mileage**: Numeric Input (non-negative, range 0-434,718).
        - **Year**: Numeric Input/Dropdown (range 1915-2021).
    - "Predict Price" button.
    - Designated area for clear display of predicted price.
- **Backend Prediction Logic (Summary)**:
    1.  Receive 5 user inputs.
    2.  Perform Feature Engineering: Calculate `car_age` (using a fixed `listed_year`, e.g., 2021 or current year) and `mileage_per_age`.
    3.  Handle Additional Model Features: Assign default values (median for numerical, mode for categorical - from EDA, detailed in Table 2 of Plan) for the ~32 other features the model expects.
    4.  Construct Full Feature Vector.
    5.  Execute Two-Stage CatBoost Model: Load `catboost_main_model.cbm` and `catboost_error_model.cbm`, get main log prediction, get error log prediction, sum them.
    6.  Output Formatting: `np.expm1(final_log_prediction)`, ensure non-negative (floor at 0).

## User Experience Goals (as per Website Plan)
- **Clarity and Simplicity**: Intuitive interface, especially for the predictor.
- **Informative Visualizations**: Engaging and help users understand pricing trends.
- **Responsive Design**: Optimal experience across desktop, tablet, mobile.
- **Performance**: Fast API responses, quick page/visualization loads (consider lazy loading).

## Key Data & Model Insights to Leverage (from Website Plan)
- Dataset: ~3 million listings (2006-2020), ~66 initial features, ~37 features post-preprocessing.
- Final Model: CatBoost (main) + CatBoost (error model on residuals). R2=0.9910, MAE ~$965.95.
- Prediction: `log_price_pred = log_price_main_model + log_price_error_model`. Then `np.expm1()` and floor at 0.
- Saved models: `catboost_main_model.cbm`, `catboost_error_model.cbm`.
- EDA provides inspiration for Home Page visualizations (price distributions, price vs. mileage/age, price by make/body type, correlation matrix, feature importance).

## Dashboard Visualizations (EDA-Based)

### Primary Visualizations (Must-Have)
1. **Price Distribution Analysis**
   - Histogram of car prices with KDE overlay
   - Log-transformed price distribution
   - Interactive filters by price range

2. **Price vs. Key Features**
   - Scatter plot: Price vs. Car Age
   - Correlation strength indicators
   - Trend lines and confidence intervals

3. **Market Segmentation**
   - Box plots: Price by Top 10 Car Makes
   - Price distribution by Body Type
   - Fuel Type price comparison
   - Interactive make/model selection

4. **Feature Correlation Matrix**
   - Heatmap of numerical feature correlations
   - Color-coded correlation strength
   - Hover details for specific correlations

5. **Numerical Feature Distributions**
   - Multi-panel histogram: Mileage, Horsepower, Age, Days on Market, Engine Displacement
   - Statistical summaries (mean, median, quartiles)

### Secondary Visualizations (Nice-to-Have)
1. **Categorical Feature Analysis**
   - Bar charts: Top makes, body types, transmission types
   - Count distributions with interactive sorting

2. **Geographic Analysis** (if location data included)
   - Price trends by region/state
   - Market density visualization

3. **Time Series Analysis**
   - Price trends over listing dates
   - Seasonal patterns (if data supports)

### Interactive Features
1. **Filtering Capabilities**
   - Price range slider
   - Make/model dropdown selection
   - Year range selection
   - Mileage range filter

2. **Dynamic Updates**
   - Real-time chart updates based on filters
   - Linked visualizations (selecting one updates others)
   - Zoom and pan functionality

3. **Export Options**
   - Download charts as PNG/PDF
   - Export filtered data as CSV
   - Share chart configurations

## Prediction Interface Requirements

### Input Form Design
1. **Grouped Inputs** (UX Priority)
   - **Basic Info**: Make, Model, Year, Mileage
   - **Performance**: Engine specs, horsepower, fuel economy
   - **Features**: Navigation, leather seats, etc.
   - **Physical**: Dimensions, seating capacity
   - **Market Info**: Days on market, seller rating

2. **User Experience Features**
   - **Smart Defaults**: Popular values pre-selected
   - **Progressive Disclosure**: Optional fields collapsed
   - **Input Validation**: Real-time feedback
   - **Auto-complete**: Make/model suggestions
   - **Help Text**: Feature explanations

3. **Prediction Results Display**
   - **Primary Result**: Large, prominent price display
   - **Confidence Interval**: Range visualization
   - **Comparison Metrics**: vs. market average
   - **Feature Impact**: Top factors influencing price
   - **Similar Vehicles**: Comparable listings (if data available)

## Success Metrics

### Business Metrics
1. **User Engagement**
   - Daily active users
   - Predictions per session
   - Dashboard interaction time
   - Feature adoption rates

2. **Accuracy Metrics**
   - Prediction accuracy vs. actual sales (if obtainable)
   - User satisfaction ratings
   - Prediction confidence intervals

3. **Performance Metrics**
   - Page load times < 3 seconds
   - Prediction response times < 2 seconds
   - Dashboard render times < 5 seconds
   - 99.9% uptime target

### User Experience Goals
1. **Usability**
   - One-click prediction capability
   - Intuitive navigation between tabs
   - Mobile-responsive design
   - Accessible interface (WCAG compliance)

2. **Value Delivery**
   - Actionable insights from visualizations
   - Clear prediction explanations
   - Comprehensive market context
   - Educational content about factors affecting price

## Competitive Advantage

### Differentiators
1. **Advanced AI**: Two-stage CatBoost model with error correction
2. **Comprehensive Analysis**: 45+ features vs. competitors' 10-15
3. **Interactive Insights**: Rich visualizations vs. static reports
4. **Instant Results**: Real-time predictions vs. batch processing
5. **Transparency**: Model explainability and confidence intervals

### Market Positioning
- **Premium Tool**: Professional-grade accuracy
- **User-Friendly**: Consumer-accessible interface
- **Data-Rich**: Comprehensive market insights
- **Reliable**: Consistent, accurate predictions 