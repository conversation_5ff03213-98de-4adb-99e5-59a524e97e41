# Project Brief: Used Car Price Prediction Platform (Website Development)

## Project Overview
This project focuses on developing a deployable SaaS website based on the "Website Development Plan: Used Car Price Prediction Platform". The primary objective is to showcase the project's capabilities in predicting used car prices using a pre-trained CatBoost + Error Model and to provide an interactive tool for users. The website will be built within the `used_cars_saas` directory.

## Core Functionalities

### 1. Home Page
- Concise introduction to the used car price prediction project.
- Engaging, data-driven visualizations (animated or 3D preferred by user) based on EDA insights (e.g., Interactive Price Distribution, 3D Scatter Plot of Price vs. Mileage vs. Car Age, Animated Feature Importance).
- Clear direction to the 'Model Predictor' tab.

### 2. 'Model Predictor' Page
- User-friendly interface for inputting specific car details (Make, Model, Number of Cylinders, Mileage, Year).
- Backend logic to augment these 5 inputs with ~32 default values (median/mode) to form the full feature set for the models.
- Real-time price prediction using the two-stage CatBoost models (`catboost_main_model.cbm` and `catboost_error_model.cbm`).
- Clear presentation of the predicted price.

## Business Goals
- Deliver a high-quality, engaging, and functional web application.
- Effectively leverage and showcase the sophisticated used car price prediction model.
- Provide an interactive and informative user experience.
- Establish a foundation for a potentially more extensive car valuation service.

## Success Criteria
- Successful deployment of the web application.
- Functional Home Page with specified interactive visualizations.
- Accurate price predictions on the 'Model Predictor' page, replicating the logic of the CatBoost + Error Model.
- Positive user feedback on usability and engagement.
- Adherence to the recommended technology stack (FastAPI, React/Vue.js, Docker).
- Responsive design for desktop, tablet, and mobile.

## Target Users
- Car dealers and automotive professionals.
- Individual car buyers and sellers.
- Automotive market analysts.
- Users interested in data science project showcases.

## Technical Constraints
- Must use the provided `catboost_main_model.cbm` and `catboost_error_model.cbm`.
- Backend must replicate the exact feature engineering and prediction logic (log transforms, `car_age`, `mileage_per_age`, combination of two models, `np.expm1`, flooring at zero).
- Frontend visualizations to be animated or 3D as preferred.
- Technology stack: FastAPI (Python backend), React or Vue.js (Frontend - User to finalize choice), Docker for deployment.
- All development to occur within the `used_cars_saas` directory.

## Timeline (Based on "Website Development Plan")
A 5-phase development approach:
1.  **Phase 1**: Backend API and Model Integration (Core Logic)
2.  **Phase 2**: Core Frontend and 'Model Predictor' Functionality
3.  **Phase 3**: Home Page Implementation and Visualization Development
4.  **Phase 4**: Comprehensive Testing and User Feedback Iteration
5.  **Phase 5**: Deployment Preparation and Go-Live 