# System Patterns: Used Car Price Prediction Platform Website

## High-Level System Architecture (as per Website Plan)

```mermaid
graph TD
    User --> FE[Frontend: React/Vue.js]
    FE --> API[Backend API: FastAPI]
    API --> MS[Model Serving Component]
    MS --> Models[CatBoost Models: main_model.cbm, error_model.cbm]
    API --> DS_Viz[Data Store for Visualizations: Optional, e.g., JSON files / Small DB]
    DS_Viz --> FE

    subgraph used_cars_saas
        direction LR
        subgraph Frontend
            FE
        end
        subgraph Backend
            API
            MS
            Models
            DS_Viz
        end
    end
```

**Components:**
1.  **Frontend (Client-Side)**: (React or Vue.js - User to finalize)
    *   Renders UI (Home Page, Model Predictor Page).
    *   Displays visualizations (animated/3D preferred).
    *   Handles user input for prediction.
    *   Communicates with Backend API.
2.  **Backend API (Server-Side)**: (FastAPI, Python)
    *   Serves data for frontend (e.g., make/model lists for dropdowns).
    *   Receives prediction requests (5 user inputs).
    *   Preprocesses inputs, augments with ~32 default values, calculates `car_age`, `mileage_per_age`.
    *   Interacts with Model Serving Component for predictions.
    *   Returns formatted predictions to frontend.
3.  **Model Serving Component**: (Integrated within FastAPI for initial simplicity)
    *   Loads `catboost_main_model.cbm` and `catboost_error_model.cbm`.
    *   Executes two-stage prediction logic (log-prediction, sum, `np.expm1`, floor at 0).
4.  **Data Store for Home Page Visualizations (Recommended in Plan)**:
    *   Summarized/pre-aggregated version of dataset (e.g., static JSON files or small DB).
    *   Ensures fast retrieval for Home Page visuals, avoiding on-the-fly aggregation from large dataset.

## Key Design Patterns & Considerations (from Website Plan)

### Backend (FastAPI)
*   **Asynchronous Operations**: FastAPI's async capabilities are beneficial for I/O-bound tasks and model serving.
*   **Data Validation**: Pydantic for request/response validation.
*   **Model Loading**: Load CatBoost models efficiently (e.g., on startup, singleton pattern if applicable).
*   **Feature Engineering Module**: Separate logic for `car_age`, `mileage_per_age` and combining user inputs with defaults.

### Frontend (React/Vue.js)
*   **Component-Based Architecture**: For UI modularity.
*   **State Management**: Appropriate state management solution (e.g., Context API, Redux, Vuex).
*   **API Service Layer**: To handle communication with the backend.
*   **Visualization Libraries**: Plotly.js, ECharts, D3.js (User to finalize) for animated/3D charts.

### Data Flow for Prediction
1.  User submits 5 inputs on Frontend.
2.  Frontend sends request to Backend API.
3.  Backend API validates inputs.
4.  Backend API performs feature engineering (calculates `car_age`, `mileage_per_age`).
5.  Backend API constructs full feature vector by merging user inputs with ~32 stored default values.
6.  Full feature vector passed to Model Serving Component.
7.  Main CatBoost model predicts log_price.
8.  Error CatBoost model predicts log_error using the same full feature vector.
9.  Predictions are summed: `final_log_pred = log_price + log_error`.
10. Result is transformed: `price = np.expm1(final_log_pred)`.
11. Price floored at 0 if negative.
12. Backend API returns formatted price to Frontend.
13. Frontend displays prediction.

### Deployment
*   **Containerization**: Docker for both frontend and backend services.
*   **Hosting**: Cloud platforms like AWS ECS, Google Cloud Run recommended for initial deployment.

### Critical Considerations from Plan:
*   **Efficient Data Pipeline for Home Page Visualizations**: Pre-aggregate data.
*   **User Experience (UX) Design**: Clarity, simplicity, responsive design.
*   **Performance Optimization**: API speed, page load, lazy loading.
*   **Scalability and Maintainability**: Clean code, framework best practices.

## Design Patterns

### Backend Patterns

#### 1. Repository Pattern (Data Access)
```python
# Pattern for dashboard data access
class DashboardDataRepository:
    def __init__(self, data_path: str):
        self.data = pd.read_parquet(data_path)
    
    def get_price_distribution(self, filters: Dict) -> Dict:
        # Apply filters and return aggregated data
        pass
    
    def get_correlation_matrix(self, features: List[str]) -> Dict:
        # Calculate and return correlation data
        pass
```

#### 2. Strategy Pattern (Model Prediction)
```python
# Different prediction strategies
class PredictionStrategy:
    def predict(self, features: Dict) -> Dict:
        raise NotImplementedError

class CatBoostDualStageStrategy(PredictionStrategy):
    def __init__(self, main_model_path: str, error_model_path: str):
        self.main_model = CatBoostRegressor()
        self.error_model = CatBoostRegressor()
        # Load models
    
    def predict(self, features: Dict) -> Dict:
        # Two-stage prediction logic
        pass
```

#### 3. Factory Pattern (Preprocessor Creation)
```python
class PreprocessorFactory:
    @staticmethod
    def create_feature_preprocessor() -> FeaturePreprocessor:
        return CatBoostFeaturePreprocessor()
    
    @staticmethod
    def create_dashboard_preprocessor() -> DashboardPreprocessor:
        return DashboardDataPreprocessor()
```

#### 4. Singleton Pattern (Model Loading)
```python
class ModelManager:
    _instance = None
    _models_loaded = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def load_models(self):
        if not self._models_loaded:
            # Load heavy models once
            self.main_model = load_catboost_model('main')
            self.error_model = load_catboost_model('error')
            self._models_loaded = True
```

### Frontend Patterns

#### 1. Component Composition Pattern
```jsx
// Dashboard composed of multiple chart components
const Dashboard = () => {
  return (
    <DashboardLayout>
      <FilterControls onFilterChange={handleFilterChange} />
      <ChartGrid>
        <PriceDistributionChart data={data.priceDistribution} />
        <PriceVsAgeChart data={data.priceVsAge} />
        <MakeComparisonChart data={data.makeComparison} />
        <CorrelationHeatmap data={data.correlations} />
      </ChartGrid>
    </DashboardLayout>
  );
};
```

#### 2. Custom Hooks Pattern
```jsx
// Reusable data fetching logic
const useDashboardData = (filters) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    fetchDashboardData(filters).then(setData);
  }, [filters]);
  
  return { data, loading };
};
```

#### 3. Provider Pattern (State Management)
```jsx
// Global state for filters and user preferences
const DashboardContext = createContext();

const DashboardProvider = ({ children }) => {
  const [filters, setFilters] = useState(defaultFilters);
  const [preferences, setPreferences] = useState(defaultPreferences);
  
  return (
    <DashboardContext.Provider value={{ filters, setFilters, preferences }}>
      {children}
    </DashboardContext.Provider>
  );
};
```

## Data Flow Patterns

### 1. Prediction Flow
```
User Input → Frontend Validation → API Request → Feature Preprocessing 
    → Main Model Prediction → Error Model Prediction → Combine Results 
    → Post-processing → API Response → Frontend Display
```

### 2. Dashboard Data Flow
```
Raw Data (Parquet) → Background Processing → Cached Aggregations 
    → API Endpoints → Frontend Charts → User Interactions → Filter Updates
    → New API Requests → Updated Visualizations
```

### 3. Model Loading Flow
```
Application Startup → Check Model Availability → Load Models into Memory 
    → Initialize Preprocessors → Ready for Predictions
```

## Performance Patterns

### 1. Lazy Loading
- **Models**: Load on first prediction request vs. startup
- **Dashboard Data**: Load chart data as components mount
- **Filters**: Load filter options dynamically

### 2. Caching Strategy
```python
# Multi-level caching
class CacheManager:
    def __init__(self):
        self.memory_cache = {}  # In-memory for fast access
        self.redis_cache = RedisClient()  # Shared cache for scaling
    
    def get_dashboard_data(self, key: str, generator_func):
        # Check memory -> Redis -> Generate -> Store
        pass
```

### 3. Batch Processing
```python
# Batch predictions for efficiency
class BatchPredictor:
    def predict_batch(self, features_list: List[Dict]) -> List[Dict]:
        # Process multiple predictions in single model call
        processed_features = [self.preprocess(f) for f in features_list]
        predictions = self.model.predict(processed_features)
        return [self.post_process(p) for p in predictions]
```

## Error Handling Patterns

### 1. Circuit Breaker Pattern
```python
class ModelCircuitBreaker:
    def __init__(self, failure_threshold=5, timeout=60):
        self.failure_count = 0
        self.failure_threshold = failure_threshold
        self.last_failure_time = None
        self.timeout = timeout
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func, *args, **kwargs):
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.timeout:
                self.state = "HALF_OPEN"
            else:
                raise CircuitBreakerOpenError()
        
        try:
            result = func(*args, **kwargs)
            self.reset()
            return result
        except Exception as e:
            self.record_failure()
            raise e
```

### 2. Graceful Degradation
```python
# Fallback to simpler model if main model fails
def predict_with_fallback(features: Dict) -> Dict:
    try:
        return catboost_predictor.predict(features)
    except Exception as e:
        logger.warning(f"CatBoost failed: {e}, using fallback")
        return simple_linear_predictor.predict(features)
```

### 3. Validation Chain
```python
class FeatureValidator:
    def __init__(self):
        self.validators = [
            RequiredFieldsValidator(),
            DataTypeValidator(),
            RangeValidator(),
            BusinessLogicValidator()
        ]
    
    def validate(self, features: Dict) -> ValidationResult:
        for validator in self.validators:
            result = validator.validate(features)
            if not result.is_valid:
                return result
        return ValidationResult.success()
```

## Security Patterns

### 1. Input Sanitization
```python
class InputSanitizer:
    @staticmethod
    def sanitize_prediction_input(data: Dict) -> Dict:
        # Remove potentially harmful content
        # Validate against schema
        # Normalize values
        return sanitized_data
```

### 2. Rate Limiting
```python
# Rate limiting for API endpoints
@router.post("/predict")
@rate_limit(requests_per_minute=10)
async def predict_price(features: CarFeatures):
    # Prediction logic
    pass
```

## Monitoring Patterns

### 1. Metrics Collection
```python
class MetricsCollector:
    def __init__(self):
        self.prediction_times = []
        self.model_accuracy_scores = []
        self.api_response_times = []
    
    def record_prediction(self, duration: float, accuracy: float):
        self.prediction_times.append(duration)
        self.model_accuracy_scores.append(accuracy)
    
    def get_metrics_summary(self) -> Dict:
        return {
            'avg_prediction_time': np.mean(self.prediction_times),
            'model_accuracy': np.mean(self.model_accuracy_scores),
            'total_predictions': len(self.prediction_times)
        }
```

### 2. Health Checks
```python
@router.get("/health")
async def health_check():
    checks = {
        'models_loaded': model_manager.are_models_loaded(),
        'memory_usage': get_memory_usage(),
        'disk_space': get_disk_space(),
        'api_latency': measure_api_latency()
    }
    
    status = 'healthy' if all(checks.values()) else 'unhealthy'
    return {'status': status, 'checks': checks}
```

## Configuration Patterns

### 1. Environment-Based Configuration
```python
class Config:
    MODEL_PATH = os.getenv('MODEL_PATH', '/app/models/')
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///./test.db')
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # Model specific settings
    ENABLE_GPU = os.getenv('ENABLE_GPU', 'False').lower() == 'true'
    MAX_BATCH_SIZE = int(os.getenv('MAX_BATCH_SIZE', '100'))
    MODEL_CACHE_SIZE = int(os.getenv('MODEL_CACHE_SIZE', '1000'))
```

### 2. Feature Flags
```python
class FeatureFlags:
    ENABLE_BATCH_PREDICTIONS = True
    ENABLE_MODEL_EXPLANATIONS = False
    ENABLE_ADVANCED_CHARTS = True
    ENABLE_DATA_EXPORT = False
    
    @classmethod
    def is_enabled(cls, feature: str) -> bool:
        return getattr(cls, feature, False)
```

## Integration Patterns

### 1. API Versioning
```python
# Support multiple API versions
@router.post("/v1/predict")
async def predict_v1(features: CarFeaturesV1):
    # Legacy prediction format
    pass

@router.post("/v2/predict")
async def predict_v2(features: CarFeaturesV2):
    # Enhanced prediction with confidence intervals
    pass
```

### 2. Data Pipeline
```python
# ETL pipeline for dashboard data
class DashboardDataPipeline:
    def extract(self) -> pd.DataFrame:
        # Load raw data from parquet
        pass
    
    def transform(self, df: pd.DataFrame) -> Dict:
        # Calculate aggregations, correlations, etc.
        pass
    
    def load(self, processed_data: Dict):
        # Cache processed data for API endpoints
        pass
    
    def run(self):
        data = self.extract()
        processed = self.transform(data)
        self.load(processed)
``` 