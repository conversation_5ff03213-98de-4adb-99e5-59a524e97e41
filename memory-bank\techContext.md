# Tech Context: Used Car Price Prediction Platform Website

## Recommended Technology Stack (as per Website Plan)

### 1. Frontend Framework (User to finalize choice)
*   **Options**: React or Vue.js.
*   **Rationale**: Mature, component-based, strong community, good for complex UIs and desired interactivity.

### 2. Backend API Framework
*   **Recommendation**: FastAPI (Python).
*   **Rationale**: High performance (async), automatic data validation & docs (Pydantic), ideal for ML model serving, modern, easy to use.

### 3. Data Visualization Libraries (JavaScript - User to finalize choice)
*   **Options**: Plotly.js, ECharts (primary recommendations from plan); D3.js (for highly custom/unique needs).
*   **Rationale**: To meet the requirement for "animated or 3D preferred" visualizations. Plotly.js & ECharts offer rich interactive 2D/3D charts. D3.js for maximum custom flexibility.

### 4. Deployment
*   **Recommendation**: Docker.
*   **Rationale**: Environment consistency, dependency management, portability, scalability, industry standard for ML apps.
*   **Hosting**: Cloud services like AWS ECS or Google Cloud Run recommended for initial deployment.

## Machine Learning Model Details (Existing)
*   **Models**: `catboost_main_model.cbm` and `catboost_error_model.cbm`.
*   **Type**: CatBoostRegressor.
*   **Prediction Logic**: 
    1.  Predict with main model (log scale).
    2.  Predict with error model using same features (log scale).
    3.  `final_y_pred_log = y_pred_log_cb_main + predicted_log_errors`.
    4.  `final_price = np.expm1(final_y_pred_log)`.
    5.  Floor price at 0 if negative.
*   **Input Features**: ~37 features in total (after preprocessing and feature engineering in original notebook).

## Data Pipeline for Prediction (Key Requirements from Plan)

### User Inputs (5 features for 'Model Predictor' page):
1.  `make_name` (Dropdown)
2.  `model_name` (Dynamic Dropdown, dependent on `make_name`)
3.  `cylinder_count` (Numeric/Dropdown - plan indicates `engine_cylinders` might be categorical I4, V6 etc. in model, need to clarify if `cylinder_count` is directly used or needs mapping)
4.  `mileage` (Numeric)
5.  `year` (Numeric - car manufacturing year)

### Backend Feature Engineering & Defaulting:
1.  **`car_age` Calculation**: `listed_year - user_inputted_year`.
    *   `listed_year` should be a fixed reference (e.g., 2021 as per plan, or current year - to be documented).
2.  **`mileage_per_age` Calculation**: `user_inputted_mileage / (calculated car_age + 0.01)` (to avoid division by zero).
3.  **Supplementary Features (~32 features)**:
    *   The remaining features required by the CatBoost models must be assigned default values.
    *   Defaults to be derived from EDA of the original dataset (`cars_df.parquet` or notebook `used_cars_project copy 3.ipynb`):
        *   **Numerical Features**: Median (e.g., `horsepower`, `daysonmarket`, `seller_rating`).
        *   **Categorical Features**: Mode (most frequent) (e.g., `dealer_zip`, `trim_name`, `body_type`, `wheel_system`, `fuel_type`).
    *   Table 2 in the "Website Development Plan" provides an illustrative list; a comprehensive list with exact default values needs to be compiled.
    *   Categorical features should be passed as strings. Placeholder like `_NAN_` for intentional missing values if model was trained that way (plan mentions this possibility).
4.  **Full Feature Vector Construction**: Combine processed user inputs and default values into the exact order and format expected by the CatBoost models.

## Key Python Dependencies for Backend (FastAPI):
*   `fastapi`
*   `uvicorn`
*   `catboost`
*   `pandas` (for data manipulation and potentially reading default value sources)
*   `numpy` (for numerical operations like `np.expm1`)
*   `pydantic` (for data validation with FastAPI)

## Development Environment Considerations:
*   All development will take place in the `used_cars_saas` directory, with subfolders like `backend` and `frontend`.
*   Dockerfiles will be needed for backend (Python/FastAPI + models) and frontend (Node.js environment for React/Vue build, then serving static files).
*   Management of large model files (`.cbm`) within Docker images or via volume mounts.

## Current Technology Stack

### Frontend (Existing)
- **Framework**: React.js with React Router
- **Styling**: Tailwind CSS
- **Charts**: Recharts library
- **HTTP Client**: Axios
- **Structure**: Component-based architecture with pages/components separation

### Backend (Existing)  
- **Framework**: FastAPI
- **Language**: Python 3.x
- **Model Format**: Currently uses LightGBM (.joblib)
- **API Structure**: Router-based with separate modules for prediction, visualization, dashboard

### Infrastructure (Existing)
- **Containerization**: Docker with docker-compose
- **Services**: Frontend (React), Backend (FastAPI), optional Nginx
- **Development**: Local development with hot reload

## Machine Learning Models (Target Integration)

### Primary Models (CatBoost)
1. **Main Model**: `catboost_main_model.cbm` (5.5GB)
   - Trained on log-transformed prices: `y_log = np.log1p(price)`
   - Features: 45 features including numerical, categorical, and engineered features
   - Loss function: RMSE
   - Evaluation metric: MAE
   - GPU trained with 10,000 iterations, early stopping at 50 rounds

2. **Error Correction Model**: `catboost_error_model.cbm` (5.4GB)
   - Trained on residuals from main model: `log_residuals = y_test_log - y_pred_log_cb`
   - Same feature set as main model
   - Two-stage prediction: `final_y_pred_log = y_pred_log_cb + predicted_log_errors`

### Model Performance (Target)
- **Combined Model MAE**: $582.46
- **Combined Model RMSE**: $1,123.72
- **Combined Model R²**: 0.9971
- **Individual CatBoost MAE**: $868.16

### Current Model (To Replace)
- **LightGBM**: `final_lgbm_model.joblib` (1.3MB)
- **Performance**: Not documented in current implementation

## Data Pipeline Requirements

### Feature Engineering (Critical)
1. **Log Transformation**: `y_log_transformed = np.log1p(y)`
2. **Age Calculation**: `car_age = current_year - year`
3. **Days Since Listed**: From `listed_date`
4. **Categorical Handling**: String conversion with '__NAN__' for missing values
5. **Sample Weights**: Used during training for better performance

### Feature Categories
1. **Numerical Features** (continuous):
   - `mileage`, `horsepower`, `engine_displacement`, `city_fuel_economy`, etc.
   
2. **Categorical Features** (string-based):
   - `make_name`, `model_name`, `body_type`, `fuel_type`, `transmission`, etc.
   
3. **Binary/Boolean Features**:
   - `is_new`, `franchise_dealer`, feature flags (has_navigation, etc.)
   
4. **Engineered Features**:
   - `car_age`, `days_since_listed`, frequency encodings

### Preprocessing Pipeline (Must Preserve)
```python
# From notebook - critical preprocessing steps:
1. Log transform target: np.log1p(price)
2. Handle categorical: astype(str).fillna('__NAN__')
3. Feature engineering: age, days calculations
4. Sample weights: calculated from data distribution
5. Train/test split: 80/20 with random_state=42
```

## Integration Requirements

### CatBoost Dependencies
```python
# Required imports
from catboost import CatBoostRegressor
import numpy as np
import pandas as pd
```

### Model Loading Pattern
```python
# Load both models
main_model = CatBoostRegressor()
main_model.load_model('catboost_main_model.cbm')

error_model = CatBoostRegressor()  
error_model.load_model('catboost_error_model.cbm')

# Two-stage prediction
y_pred_log_main = main_model.predict(X_processed)
predicted_log_errors = error_model.predict(X_processed)
final_y_pred_log = y_pred_log_main + predicted_log_errors
final_price = np.expm1(final_y_pred_log)  # Inverse log transform
```

## API Design Changes Required

### Current API Issues
1. **Model Type**: Uses LightGBM instead of CatBoost
2. **Feature Processing**: Simplified preprocessing vs notebook requirements
3. **Single Model**: No error correction model integration
4. **Input Validation**: May not match exact feature requirements

### Required API Modifications
1. **Model Loading**: Replace LGBM with dual CatBoost models
2. **Preprocessing**: Implement exact notebook pipeline
3. **Prediction Logic**: Two-stage prediction with error correction
4. **Feature Validation**: Ensure 45 feature compatibility
5. **Response Format**: Include confidence/error bounds

## Performance Considerations

### Model Size Challenges
- **Total Model Size**: 11GB (both models)
- **Loading Time**: Significant initial load time
- **Memory Usage**: High RAM requirements during serving
- **Deployment**: Large container images, slower deployments

### Optimization Strategies
1. **Model Loading**: Load once at startup, keep in memory
2. **Preprocessing**: Cache preprocessing artifacts
3. **Batch Predictions**: Support multiple predictions per request
4. **Resource Management**: Monitor memory usage

## Development Environment

### Required Python Packages
```
catboost>=1.0.0
fastapi>=0.68.0
pandas>=1.3.0
numpy>=1.21.0
pydantic>=1.8.0
uvicorn>=0.15.0
```

### Docker Considerations
- **Base Image**: Need CUDA support for CatBoost if using GPU
- **Model Storage**: Volume mounting for large model files
- **Build Time**: Longer builds due to model file copying
- **Runtime**: Higher memory allocation required 