{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚗 US Used Cars Price Prediction Project (2006-2020)\n", "\n", "## Project Overview\n", "This notebook contains a comprehensive analysis and machine learning model for predicting used car prices using a dataset of approximately 3 million used car listings across the United States from 2006–2020.\n", "\n", "### Dataset Information\n", "- **Size**: ~3 million records, 66 features per car\n", "- **Source**: CarGurus listings (web-scraped data)\n", "- **Features**: Numeric (mileage, engine size, year, horsepower) and categorical (make, model, body type, fuel type)\n", "- **Target**: Car price prediction\n", "\n", "### Project Goals\n", "1. Perform comprehensive exploratory data analysis (EDA)\n", "2. Engineer relevant features for price prediction\n", "3. Build and evaluate machine learning models\n", "4. Deploy the best model as a web application"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📚 Table of Contents\n", "\n", "1. [Environment Setup & Imports](#1-environment-setup--imports)\n", "2. [Data Loading & Initial Exploration](#2-data-loading--initial-exploration)\n", "3. [Data Cleaning & Preprocessing](#3-data-cleaning--preprocessing)\n", "4. [Exploratory Data Analysis (EDA)](#4-exploratory-data-analysis-eda)\n", "5. [Feature Engineering](#5-feature-engineering)\n", "6. [Model Development](#6-model-development)\n", "7. [Model Evaluation](#7-model-evaluation)\n", "8. [Results & Conclusions](#8-results--conclusions)\n", "9. [Next Steps](#9-next-steps)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 1. Environment Setup & Imports\n", "\n", "Setting up the necessary libraries and configurations for the analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core data manipulation and analysis\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# Data visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "# Machine learning\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import LinearRegression, Ridge, Lasso\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "\n", "# Advanced ML libraries\n", "import lightgbm as lgb\n", "import catboost as cb\n", "import xgboost as xgb\n", "\n", "# Utilities\n", "import warnings\n", "import os\n", "from datetime import datetime\n", "import kagglehub\n", "\n", "# Configuration\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8')\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "\n", "print(\"✅ All libraries imported successfully!\")\n", "print(f\"📅 Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 2. Data Loading & Initial Exploration\n", "\n", "Loading the dataset and performing initial exploration to understand the data structure."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Download dataset from Kaggle\n", "print(\"📥 Downloading dataset from Kaggle...\")\n", "path = kagglehub.dataset_download(\"ananaymital/us-used-cars-dataset\")\n", "print(f\"📁 Dataset downloaded to: {path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define optimized data types for memory efficiency\n", "data_types = {\n", "    'vin': 'object',\n", "    'back_legroom': 'object',\n", "    'bed': 'object',\n", "    'bed_height': 'object',\n", "    'bed_length': 'object',\n", "    'body_type': 'category',\n", "    'cabin': 'object',\n", "    'city': 'category',\n", "    'city_fuel_economy': 'float32',\n", "    'combine_fuel_economy': 'float32',\n", "    'daysonmarket': 'int16',\n", "    'dealer_zip': 'object',\n", "    'description': 'object',\n", "    'engine_cylinders': 'category',\n", "    'engine_displacement': 'float32',\n", "    'engine_type': 'object',\n", "    'exterior_color': 'category',\n", "    'fleet': 'object',\n", "    'frame_damaged': 'object',\n", "    'franchise_dealer': 'bool',\n", "    'franchise_make': 'object',\n", "    'front_legroom': 'object',\n", "    'fuel_tank_volume': 'object',\n", "    'fuel_type': 'object',\n", "    'has_accidents': 'object',\n", "    'height': 'object',\n", "    'highway_fuel_economy': 'float32',\n", "    'horsepower': 'float32',\n", "    'interior_color': 'object',\n", "    'isCab': 'object',\n", "    'is_certified': 'float32',\n", "    'is_cpo': 'object',\n", "    'is_new': 'bool',\n", "    'is_oemcpo': 'object',\n", "    'latitude': 'float32',\n", "    'length': 'object',\n", "    'listed_date': 'object',\n", "    'listing_color': 'object',\n", "    'listing_id': 'int64',\n", "    'longitude': 'float32',\n", "    'main_picture_url': 'object',\n", "    'major_options': 'object',\n", "    'make_name': 'object',\n", "    'maximum_seating': 'object',\n", "    'mileage': 'float32',\n", "    'model_name': 'object',\n", "    'owner_count': 'float32',\n", "    'power': 'object',\n", "    'price': 'float32',\n", "    'salvage': 'object',\n", "    'savings_amount': 'int64',\n", "    'seller_rating': 'float32',\n", "    'sp_id': 'float32',\n", "    'sp_name': 'object',\n", "    'theft_title': 'object',\n", "    'torque': 'object',\n", "    'transmission': 'object',\n", "    'transmission_display': 'object',\n", "    'trimId': 'object',\n", "    'trim_name': 'object',\n", "    'vehicle_damage_category': 'float32',\n", "    'wheel_system': 'object',\n", "    'wheel_system_display': 'object',\n", "    'wheelbase': 'object',\n", "    'width': 'object',\n", "    'year': 'int32'\n", "}\n", "\n", "print(\"📋 Data types defined for memory optimization\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset with optimized data types\n", "df_path = os.path.join(path, 'used_cars_data.csv')\n", "print(f\"📂 Loading dataset from: {df_path}\")\n", "\n", "# Load data with progress indication\n", "print(\"⏳ Loading data... (this may take a few minutes for 3M records)\")\n", "cars_df = pd.read_csv(df_path, dtype=data_types)\n", "\n", "print(f\"✅ Dataset loaded successfully!\")\n", "print(f\"📊 Shape: {cars_df.shape}\")\n", "print(f\"💾 Memory usage: {cars_df.memory_usage(deep=True).sum() / 1024**3:.2f} GB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initial data exploration\n", "print(\"🔍 INITIAL DATA EXPLORATION\")\n", "print(\"=\" * 50)\n", "\n", "# Basic information\n", "print(f\"Dataset shape: {cars_df.shape}\")\n", "print(f\"Number of features: {cars_df.shape[1]}\")\n", "print(f\"Number of records: {cars_df.shape[0]:,}\")\n", "\n", "# Display first few rows\n", "print(\"\\n📋 First 5 rows:\")\n", "cars_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data types and memory usage\n", "print(\"📊 DATA TYPES AND MEMORY USAGE\")\n", "print(\"=\" * 50)\n", "cars_df.info(memory_usage='deep')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Statistical summary\n", "print(\"📈 STATISTICAL SUMMARY\")\n", "print(\"=\" * 50)\n", "cars_df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 3. Data Cleaning & Preprocessing\n", "\n", "Identifying and handling missing values, outliers, and data quality issues."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Missing values analysis\n", "print(\"🔍 MISSING VALUES ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "missing_data = cars_df.isnull().sum()\n", "missing_percent = (missing_data / len(cars_df)) * 100\n", "\n", "missing_df = pd.DataFrame({\n", "    'Missing Count': missing_data,\n", "    'Missing Percentage': missing_percent\n", "}).sort_values('Missing Percentage', ascending=False)\n", "\n", "# Show only columns with missing values\n", "missing_df = missing_df[missing_df['Missing Count'] > 0]\n", "\n", "print(f\"Columns with missing values: {len(missing_df)}\")\n", "print(\"\\nTop 20 columns with most missing values:\")\n", "missing_df.head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize missing values\n", "plt.figure(figsize=(12, 8))\n", "top_missing = missing_df.head(20)\n", "\n", "plt.barh(range(len(top_missing)), top_missing['Missing Percentage'])\n", "plt.yticks(range(len(top_missing)), top_missing.index)\n", "plt.xlabel('Missing Percentage (%)')\n", "plt.title('Top 20 Features with Missing Values')\n", "plt.gca().invert_yaxis()\n", "\n", "# Add percentage labels\n", "for i, v in enumerate(top_missing['Missing Percentage']):\n", "    plt.text(v + 0.5, i, f'{v:.1f}%', va='center')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Target variable analysis\n", "print(\"🎯 TARGET VARIABLE ANALYSIS (PRICE)\")\n", "print(\"=\" * 50)\n", "\n", "# Basic statistics\n", "print(f\"Price statistics:\")\n", "print(f\"  Count: {cars_df['price'].count():,}\")\n", "print(f\"  Mean: ${cars_df['price'].mean():,.2f}\")\n", "print(f\"  Median: ${cars_df['price'].median():,.2f}\")\n", "print(f\"  Std: ${cars_df['price'].std():,.2f}\")\n", "print(f\"  Min: ${cars_df['price'].min():,.2f}\")\n", "print(f\"  Max: ${cars_df['price'].max():,.2f}\")\n", "\n", "# Check for missing prices\n", "missing_prices = cars_df['price'].isnull().sum()\n", "print(f\"\\nMissing prices: {missing_prices:,} ({missing_prices/len(cars_df)*100:.2f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 4. Exploratory Data Analysis (EDA)\n", "\n", "Deep dive into the data to understand patterns, relationships, and insights."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Price distribution analysis\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Histogram\n", "axes[0, 0].hist(cars_df['price'], bins=100, alpha=0.7, color='skyblue')\n", "axes[0, 0].set_title('Price Distribution')\n", "axes[0, 0].set_xlabel('Price ($)')\n", "axes[0, 0].set_ylabel('Frequency')\n", "\n", "# Log-scale histogram\n", "axes[0, 1].hist(np.log1p(cars_df['price']), bins=100, alpha=0.7, color='lightgreen')\n", "axes[0, 1].set_title('Price Distribution (Log Scale)')\n", "axes[0, 1].set_xlabel('Log(Price + 1)')\n", "axes[0, 1].set_ylabel('Frequency')\n", "\n", "# Box plot\n", "axes[1, 0].boxplot(cars_df['price'])\n", "axes[1, 0].set_title('Price Box Plot')\n", "axes[1, 0].set_ylabel('Price ($)')\n", "\n", "# Q-Q plot\n", "from scipy import stats\n", "stats.probplot(cars_df['price'].dropna(), dist=\"norm\", plot=axes[1, 1])\n", "axes[1, 1].set_title('Q-Q Plot (Normal Distribution)')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Key categorical features analysis\n", "categorical_features = ['make_name', 'body_type', 'fuel_type', 'transmission']\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(20, 12))\n", "axes = axes.ravel()\n", "\n", "for i, feature in enumerate(categorical_features):\n", "    if feature in cars_df.columns:\n", "        top_values = cars_df[feature].value_counts().head(15)\n", "        \n", "        axes[i].barh(range(len(top_values)), top_values.values)\n", "        axes[i].set_yticks(range(len(top_values)))\n", "        axes[i].set_yticklabels(top_values.index)\n", "        axes[i].set_title(f'Top 15 {feature.replace(\"_\", \" \").title()}')\n", "        axes[i].set_xlabel('Count')\n", "        \n", "        # Add count labels\n", "        for j, v in enumerate(top_values.values):\n", "            axes[i].text(v + max(top_values.values) * 0.01, j, f'{v:,}', va='center')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 5. Feature Engineering\n", "\n", "Creating new features and transforming existing ones to improve model performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a working copy for feature engineering\n", "df_features = cars_df.copy()\n", "\n", "print(\"🔧 FEATURE ENGINEERING\")\n", "print(\"=\" * 50)\n", "\n", "# 1. Age feature\n", "current_year = 2024\n", "df_features['age'] = current_year - df_features['year']\n", "print(\"✅ Created 'age' feature\")\n", "\n", "# 2. Price per mile feature\n", "df_features['price_per_mile'] = df_features['price'] / (df_features['mileage'] + 1)  # +1 to avoid division by zero\n", "print(\"✅ Created 'price_per_mile' feature\")\n", "\n", "# 3. Luxury brand indicator\n", "luxury_brands = ['BMW', 'Mercedes-Benz', 'Audi', 'Lexus', 'Cadillac', 'Lincoln', 'Infiniti', 'Acura']\n", "df_features['is_luxury'] = df_features['make_name'].isin(luxury_brands)\n", "print(\"✅ Created 'is_luxury' feature\")\n", "\n", "# 4. Fuel efficiency category\n", "df_features['fuel_efficiency_category'] = pd.cut(\n", "    df_features['city_fuel_economy'], \n", "    bins=[0, 15, 25, 35, float('inf')], \n", "    labels=['Low', 'Medium', 'High', 'Very High']\n", ")\n", "print(\"✅ Created 'fuel_efficiency_category' feature\")\n", "\n", "print(f\"\\nTotal features after engineering: {df_features.shape[1]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 6. Model Development\n", "\n", "Building and training machine learning models for price prediction."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare data for modeling\n", "print(\"🤖 MODEL DEVELOPMENT\")\n", "print(\"=\" * 50)\n", "\n", "# Select features for modeling (example subset)\n", "numeric_features = ['year', 'mileage', 'horsepower', 'engine_displacement', \n", "                   'city_fuel_economy', 'highway_fuel_economy', 'age']\n", "\n", "categorical_features = ['make_name', 'body_type', 'fuel_type', 'transmission']\n", "\n", "# Filter available features\n", "available_numeric = [f for f in numeric_features if f in df_features.columns]\n", "available_categorical = [f for f in categorical_features if f in df_features.columns]\n", "\n", "print(f\"Available numeric features: {len(available_numeric)}\")\n", "print(f\"Available categorical features: {len(available_categorical)}\")\n", "\n", "# Create feature matrix\n", "X_numeric = df_features[available_numeric].fillna(df_features[available_numeric].median())\n", "y = df_features['price'].dropna()\n", "\n", "# Align X and y (remove rows where price is missing)\n", "valid_indices = df_features['price'].notna()\n", "X_numeric = X_numeric[valid_indices]\n", "\n", "print(f\"\\nFinal dataset shape: {X_numeric.shape}\")\n", "print(f\"Target variable shape: {y.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_numeric, y, test_size=0.2, random_state=42\n", ")\n", "\n", "print(f\"Training set shape: {X_train.shape}\")\n", "print(f\"Test set shape: {X_test.shape}\")\n", "\n", "# Scale features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(\"✅ Data prepared for modeling\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 7. Model Evaluation\n", "\n", "Training multiple models and evaluating their performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model evaluation function\n", "def evaluate_model(model, X_train, X_test, y_train, y_test, model_name):\n", "    \"\"\"Evaluate a model and return metrics\"\"\"\n", "    \n", "    # Train the model\n", "    model.fit(X_train, y_train)\n", "    \n", "    # Make predictions\n", "    y_pred_train = model.predict(X_train)\n", "    y_pred_test = model.predict(X_test)\n", "    \n", "    # Calculate metrics\n", "    train_mae = mean_absolute_error(y_train, y_pred_train)\n", "    test_mae = mean_absolute_error(y_test, y_pred_test)\n", "    train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))\n", "    test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))\n", "    train_r2 = r2_score(y_train, y_pred_train)\n", "    test_r2 = r2_score(y_test, y_pred_test)\n", "    \n", "    return {\n", "        'Model': model_name,\n", "        'Train MAE': train_mae,\n", "        'Test MAE': test_mae,\n", "        'Train RMSE': train_rmse,\n", "        'Test RMSE': test_rmse,\n", "        'Train R²': train_r2,\n", "        'Test R²': test_r2\n", "    }\n", "\n", "print(\"📊 MODEL EVALUATION\")\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train and evaluate multiple models\n", "models = {\n", "    'Linear Regression': LinearRegression(),\n", "    'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),\n", "    'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42)\n", "}\n", "\n", "results = []\n", "\n", "for name, model in models.items():\n", "    print(f\"Training {name}...\")\n", "    \n", "    # Use scaled data for Linear Regression, original for tree-based models\n", "    if 'Linear' in name:\n", "        result = evaluate_model(model, X_train_scaled, X_test_scaled, y_train, y_test, name)\n", "    else:\n", "        result = evaluate_model(model, X_train, X_test, y_train, y_test, name)\n", "    \n", "    results.append(result)\n", "    print(f\"✅ {name} completed\")\n", "\n", "# Create results DataFrame\n", "results_df = pd.DataFrame(results)\n", "print(\"\\n📈 MODEL COMPARISON:\")\n", "results_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 8. Results & Conclusions\n", "\n", "Summary of findings and model performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize model performance\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "\n", "metrics = ['Test MAE', 'Test RMSE', 'Test R²']\n", "colors = ['skyblue', 'lightgreen', 'salmon']\n", "\n", "for i, metric in enumerate(metrics):\n", "    axes[i].bar(results_df['Model'], results_df[metric], color=colors[i], alpha=0.7)\n", "    axes[i].set_title(f'{metric} Comparison')\n", "    axes[i].set_ylabel(metric)\n", "    axes[i].tick_params(axis='x', rotation=45)\n", "    \n", "    # Add value labels on bars\n", "    for j, v in enumerate(results_df[metric]):\n", "        if metric == 'Test R²':\n", "            axes[i].text(j, v + 0.01, f'{v:.3f}', ha='center', va='bottom')\n", "        else:\n", "            axes[i].text(j, v + max(results_df[metric]) * 0.01, f'{v:,.0f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 9. Next Steps\n", "\n", "### Recommendations for Further Development:\n", "\n", "1. **Feature Engineering Improvements**:\n", "   - Extract more features from text descriptions\n", "   - Create interaction features between important variables\n", "   - Handle categorical variables with high cardinality\n", "\n", "2. **Model Optimization**:\n", "   - Hyperparameter tuning using GridSearchCV or RandomizedSearchCV\n", "   - Try advanced models like XGBoost, LightGBM, or CatBoost\n", "   - Implement ensemble methods\n", "\n", "3. **Data Quality Improvements**:\n", "   - Better handling of missing values\n", "   - Outlier detection and treatment\n", "   - Data validation and cleaning\n", "\n", "4. **Deployment**:\n", "   - Create a web application for price predictions\n", "   - Implement model monitoring and retraining pipeline\n", "   - Add model explainability features\n", "\n", "5. **Business Value**:\n", "   - Develop pricing recommendations for dealers\n", "   - Create market analysis dashboards\n", "   - Implement real-time price alerts\""]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}