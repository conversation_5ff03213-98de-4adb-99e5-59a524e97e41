import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import lightgbm as lgb
import ast
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
from catboost import CatBoostRegressor, Pool
from sklearn.impute import SimpleImputer
from category_encoders import TargetEncoder

import kagglehub

path = kagglehub.dataset_download("ananaymital/us-used-cars-dataset")
print("Path to dataset files:", path)

df_path = r"C:\Users\<USER>\.cache\kagglehub\datasets\ananaymital\us-used-cars-dataset\versions\1\used_cars_data.csv"

data_types = {
    'vin': 'object',
    'back_legroom': 'object',
    'bed': 'object',
    'bed_height': 'object',
    'bed_length': 'object',
    'body_type': 'category',
    'cabin': 'object',
    'city': 'category',
    'city_fuel_economy': 'float32',
    'combine_fuel_economy': 'float32',
    'daysonmarket': 'int16',
    'dealer_zip': 'object',
    'description': 'object',
    'engine_cylinders': 'category',
    'engine_displacement': 'float32',
    'engine_type': 'object',
    'exterior_color': 'category',
    'fleet': 'object',
    'frame_damaged': 'object',
    'franchise_dealer': 'bool',
    'franchise_make': 'object',
    'front_legroom': 'object',
    'fuel_tank_volume': 'object',
    'fuel_type': 'object',
    'has_accidents': 'object',
    'height': 'object',
    'highway_fuel_economy': 'float32',
    'horsepower': 'float32',
    'interior_color': 'object',
    'isCab': 'object',
    'is_certified': 'float32',
    'is_cpo': 'object',
    'is_new': 'bool',
    'is_oemcpo': 'object',
    'latitude': 'float32',
    'length': 'object',
    'listed_date': 'object',
    'listing_color': 'object',
    'listing_id': 'int64',
    'longitude': 'float32',
    'main_picture_url': 'object',
    'major_options': 'object',
    'make_name': 'object',
    'maximum_seating': 'object',
    'mileage': 'float32',
    'model_name': 'object',
    'owner_count': 'float32',
    'power': 'object',
    'price': 'float32',
    'salvage': 'object',
    'savings_amount': 'int64',
    'seller_rating': 'float32',
    'sp_id': 'float32',
    'sp_name': 'object',
    'theft_title': 'object',
    'torque': 'object',
    'transmission': 'object',
    'transmission_display': 'object',
    'trimId': 'object',
    'trim_name': 'object',
    'vehicle_damage_category': 'float32',
    'wheel_system': 'object',
    'wheel_system_display': 'object',
    'wheelbase': 'object',
    'width': 'object',
    'year': 'int32'
}

cars_df = pd.read_csv(df_path, dtype=data_types)

cars_df.info()

cars_df.describe()

cars_df.head(10)

pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)

missing_percentages = (cars_df.isnull().sum() / len(cars_df)) * 100
sorted_missing_percentages = missing_percentages.sort_values(ascending=False)
print(sorted_missing_percentages)

missing_threshold = 0.45
missing_percentages = cars_df.isnull().sum() / len(cars_df)
columns_to_drop = missing_percentages[missing_percentages >= missing_threshold].index
cars_df.drop(columns=columns_to_drop, inplace=True)
print(f"Columns dropped: {list(columns_to_drop)}")
print(f"New shape of cars_df: {cars_df.shape}")

cars_df.info()

X = cars_df.drop('price', axis=1) 
y = cars_df['price']

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

y_train_log = np.log1p(y_train)