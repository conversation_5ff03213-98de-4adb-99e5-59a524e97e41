from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Optional
import random # For sample data
import pandas as pd # Ensure pandas is imported for DataFrame creation if needed by model_loader
import numpy as np # For SHAP calculations

from core.model_loader import ModelLoader, model_loader_instance # Ensure ModelLoader class is imported for type hinting
from .schemas import PredictionInput, PredictionOutput, DropdownOptionsResponse, PriceDistributionParams, PriceDistributionResponse, PriceDistributionDataPoint, Scatter3DParams, Scatter3DDataPoint, Scatter3DResponse, FeatureImportanceDataPoint, FeatureImportanceResponse, ShapValueDataPoint, PredictionExplanationResponse # Added new schemas

router = APIRouter()

# Dependency to get the loaded model instance
# This isn't strictly necessary if model_loader_instance is a global singleton,
# but shows a pattern if more complex dependencies were needed.
def get_model_loader() -> ModelLoader: # Add return type hint
    if model_loader_instance is None:
        raise HTTPException(status_code=503, detail="Model loader is not available. Please check server logs.")
    return model_loader_instance

@router.post("/predict", response_model=PredictionOutput, tags=["Prediction"])
async def predict_price(input_data: PredictionInput, loader: ModelLoader = Depends(get_model_loader)):
    """
    Predict the price of a used car based on its features.

    - **make_name**: Brand of the car (e.g., Toyota)
    - **model_name**: Model of the car (e.g., Camry)
    - **cylinder_count**: Number of engine cylinders (e.g., 4.0, 6.0)
    - **mileage**: Current mileage of the car
    - **year**: Manufacturing year of the car
    """
    try:
        user_inputs = input_data.model_dump() # Convert Pydantic model to dict
        predicted_price = loader.predict(user_inputs)
        return PredictionOutput(predicted_price=predicted_price)
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except RuntimeError as re:
        # This typically means models aren't loaded, a server-side issue
        print(f"RuntimeError during prediction: {re}") # Log for server admin
        raise HTTPException(status_code=503, detail="Model service temporarily unavailable. Please try again later.")
    except Exception as e:
        # Catch-all for other unexpected errors
        print(f"Unexpected error during prediction: {e}") # Log for server admin
        raise HTTPException(status_code=500, detail="An unexpected error occurred during prediction.")

@router.get("/dropdown-options", response_model=DropdownOptionsResponse, tags=["Prediction"])
async def get_dropdown_options(loader: ModelLoader = Depends(get_model_loader)):
    """
    Get options for populating dropdowns on the frontend, such as car makes, models, and cylinder counts.
    """
    try:
        make_model_map = loader.make_model_map

        # As per plan Table 1: Unique values for cylinder_count from dataset
        # These are hardcoded as per the plan document for now.
        # Could be dynamically derived from feature_defaults.json or feature_types.json if needed and available.
        cylinder_counts_options = sorted([2.0, 3.0, 4.0, 5.0, 6.0, 8.0, 10.0, 12.0, 16.0])

        if not make_model_map:
            # This might happen if the make_model_map.json was not loaded correctly
             raise HTTPException(status_code=503, detail="Make and model options are currently unavailable.")

        return DropdownOptionsResponse(
            make_model_map=make_model_map,
            cylinder_counts=cylinder_counts_options
        )
    except Exception as e:
        print(f"Error fetching dropdown options: {e}")
        raise HTTPException(status_code=500, detail="Could not retrieve dropdown options.")

@router.post("/price-distribution", response_model=PriceDistributionResponse, tags=["Visualizations"])
async def get_price_distribution_data(params: PriceDistributionParams, loader: ModelLoader = Depends(get_model_loader)): # Added loader dependency
    """
    Get data for price distribution visualization.
    Currently returns SAMPLE DATA. TODO: Implement actual data loading and filtering from cars_df.parquet.
    """
    print(f"Received price distribution params: {params}")
    # --- SAMPLE DATA LOGIC ---
    # In a real implementation, this would query/filter cars_df.parquet
    sample_data_points = []
    base_price = 5000
    if params.make_name == "Ford":
        base_price = 7000
    elif params.make_name == "Toyota":
        base_price = 6000

    if params.body_type == "SUV":
        base_price *= 1.2
    elif params.body_type == "Sedan":
        base_price *= 0.9

    if params.year_min and params.year_min > 2015:
        base_price *= (1 + (params.year_min - 2015) * 0.05)
    if params.year_max and params.year_max < 2020:
        base_price *= (1 - (2020 - params.year_max) * 0.05)

    for i in range(10): # 10 bins
        bin_start = base_price + (i * 2000) + random.randint(-500, 500)
        bin_end = bin_start + 2000
        count = random.randint(5, 50)
        if params.make_name: # Simulate some effect of filtering
            count = random.randint(2, (i*2) + 5) if i < 5 else random.randint(1, 10)
        sample_data_points.append(
            PriceDistributionDataPoint(price_bin_start=bin_start, price_bin_end=bin_end, count=count)
        )

    return PriceDistributionResponse(
        data=sample_data_points,
        filters_applied=params
    )

@router.post("/scatter-3d-data", response_model=Scatter3DResponse, tags=["Visualizations"])
async def get_scatter_3d_data(params: Scatter3DParams, loader: ModelLoader = Depends(get_model_loader)): # Added loader dependency
    """
    Get data for a 3D scatter plot (e.g., Price vs. Mileage vs. Car Age).
    Currently returns SAMPLE DATA. TODO: Implement actual data loading and sampling.
    """
    print(f"Received scatter 3D params: {params}")
    sample_data = []
    num_points = params.sample_size

    makes_for_sample = ["Ford", "Toyota", "Honda", "BMW", "Audi", "Mercedes-Benz", "Chevrolet", "Nissan"]
    body_types_for_sample = ["SUV", "Sedan", "Truck", "Coupe", "Hatchback"]

    for i in range(num_points):
        base_mileage = random.randint(5000, 150000)
        base_age = random.randint(1, 12)
        base_price = 30000 - (base_mileage * 0.1) - (base_age * 1000) + random.randint(-5000, 5000)
        base_price = max(1000, base_price) # Ensure positive price

        current_make = params.make_name if params.make_name else random.choice(makes_for_sample)
        current_body_type = params.body_type if params.body_type else random.choice(body_types_for_sample)

        # Adjust price based on make/body_type for more varied sample data
        if current_make == "BMW" or current_make == "Audi" or current_make == "Mercedes-Benz":
            base_price *= 1.5
        if current_body_type == "Truck" or current_body_type == "SUV":
            base_price *= 1.2
        elif current_body_type == "Hatchback":
             base_price *= 0.8

        sample_data.append(
            Scatter3DDataPoint(
                price=max(500, base_price + random.uniform(-2000, 2000)),
                mileage=base_mileage + random.uniform(-2000, 2000),
                car_age=max(0, base_age + random.uniform(-1,1)),
                make_name=current_make,
                body_type=current_body_type
            )
        )

    return Scatter3DResponse(
        data=sample_data,
        filters_applied=params
    )

@router.get("/feature-importance", response_model=FeatureImportanceResponse, tags=["Visualizations"])
async def get_feature_importance_data(top_n: Optional[int] = 15, model_type: str = "main", loader: ModelLoader = Depends(get_model_loader)):
    """
    Get feature importance data from the trained models.
    `model_type` can be 'main' or 'error'.
    Automatically falls back to error model if main model is not available.
    """
    if model_type not in ["main", "error"]:
        raise HTTPException(status_code=400, detail="Invalid model_type. Choose 'main' or 'error'.")

    model_to_use = None
    model_name_str = ""

    # Try to use requested model first, then fall back to available model
    if model_type == "main":
        if loader.main_model:
            model_to_use = loader.main_model
            model_name_str = "Main Model"
        elif loader.error_model:
            # Fall back to error model if main model not available
            model_to_use = loader.error_model
            model_name_str = "Error Model (Fallback)"
            print("Main model not available, using error model for feature importance")
    else: # error model requested
        model_to_use = loader.error_model
        model_name_str = "Error Model"

    if not model_to_use:
        # Check what models are available for better error message
        available_models = []
        if loader.main_model:
            available_models.append("main")
        if loader.error_model:
            available_models.append("error")

        if available_models:
            raise HTTPException(
                status_code=503,
                detail=f"Requested {model_type} model not loaded. Available models: {', '.join(available_models)}"
            )
        else:
            raise HTTPException(status_code=503, detail="No models are currently loaded.")

    try:
        # Get feature names from the loader, which should match the training features
        feature_names = loader.model_features
        if not feature_names:
             raise HTTPException(status_code=503, detail="Model feature names not available.")

        importances = model_to_use.get_feature_importance()

        # Create a list of (feature_name, importance) tuples
        # Ensure feature_names align with the order of importance scores if CatBoost returns them without names
        # CatBoost get_feature_importance() usually returns an array in the order of training features.
        if len(feature_names) != len(importances):
            # This case should ideally not happen if model_features list is correct
            # and matches the features model was trained on.
            print(f"Mismatch len feature_names ({len(feature_names)}) vs importances ({len(importances)})")
            # Fallback: try to get names from model if possible (some models store them)
            try:
                internal_model_feature_names = model_to_use.feature_names_
                if len(internal_model_feature_names) == len(importances):
                    feature_names = internal_model_feature_names
                else:
                    raise HTTPException(status_code=500, detail="Feature name and importance score mismatch.")
            except AttributeError:
                 raise HTTPException(status_code=500, detail="Could not reliably map importance scores to feature names.")

        feature_importance_list = [
            FeatureImportanceDataPoint(feature_name=name, importance=float(imp))
            for name, imp in zip(feature_names, importances)
        ]

        # Sort by importance (descending)
        feature_importance_list.sort(key=lambda x: x.importance, reverse=True)

        data_to_return = feature_importance_list
        actual_top_n = None
        if top_n and top_n > 0:
            data_to_return = feature_importance_list[:top_n]
            actual_top_n = len(data_to_return)

        return FeatureImportanceResponse(
            data=data_to_return,
            model_type=model_name_str,
            top_n=actual_top_n
        )
    except Exception as e:
        print(f"Error getting feature importance for {model_name_str}: {e}")
        raise HTTPException(status_code=500, detail=f"Could not retrieve feature importance: {str(e)}")

@router.post("/prediction-explanation", response_model=PredictionExplanationResponse, tags=["Prediction"])
async def get_prediction_explanation(input_data: PredictionInput, loader: ModelLoader = Depends(get_model_loader)):
    """
    Get detailed explanation of a specific prediction using SHAP values.
    Shows how each feature contributed to the final price prediction.
    """
    try:
        # First get the prediction
        user_inputs = input_data.model_dump()
        predicted_price = loader.predict(user_inputs)

        # Get the feature dictionary used for prediction
        feature_dict = loader.get_prediction_data_dict(user_inputs)
        df_for_prediction = pd.DataFrame([feature_dict], columns=loader.model_features)

        # Ensure dtypes are consistent
        for col in loader.feature_types.get("categorical", []):
            if col in df_for_prediction.columns:
                df_for_prediction[col] = df_for_prediction[col].astype(str).fillna('__NAN__')

        # Get SHAP values from CatBoost (requires Pool object with categorical features specified)
        from catboost import Pool

        # Get categorical feature indices for Pool creation
        categorical_features = []
        for i, feature_name in enumerate(loader.model_features):
            if feature_name in loader.feature_types.get("categorical", []):
                categorical_features.append(i)

        pool = Pool(df_for_prediction, cat_features=categorical_features)
        shap_values = loader.main_model.get_feature_importance(pool, type='ShapValues')

        # Extract SHAP values - CatBoost returns a 2D array where first row is for our single prediction
        shap_row = shap_values[0]
        shap_array = np.array(shap_row)
        num_features = len(loader.model_features)

        # CatBoost SHAP values: [feature_contributions..., base_value]
        # Check if we have base value as last element
        if shap_array.shape[0] > num_features:
            base_value = float(shap_array[-1])
            feature_shap_values = shap_array[:num_features]
        else:
            base_value = 0.0
            feature_shap_values = shap_array

        # Create human-readable explanations
        shap_contributions = []
        for i, (feature_name, shap_value) in enumerate(zip(loader.model_features, feature_shap_values)):
            feature_value = str(feature_dict.get(feature_name, "Unknown"))

            # Format feature value for display
            if feature_name == "mileage":
                feature_value = f"{int(float(feature_value)):,} miles"
            elif feature_name == "year":
                feature_value = f"{feature_value}"
            elif feature_name == "cylinder_count":
                feature_value = f"{feature_value} cylinders"

            # Calculate impact in dollars
            # SHAP values are in log space, so we need to convert properly
            # For log-transformed target: impact = exp(base + shap) - exp(base)
            # This gives the actual dollar impact of this feature
            base_price = np.exp(base_value)
            impact_dollars = np.exp(base_value + shap_value) - base_price

            # Categorize impact levels
            abs_impact = abs(impact_dollars)
            if abs_impact > 500:
                if impact_dollars > 0:
                    impact_description = f"Increases price by ${impact_dollars:,.0f}"
                else:
                    impact_description = f"Decreases price by ${abs_impact:,.0f}"
            elif abs_impact > 100:
                if impact_dollars > 0:
                    impact_description = f"Increases price by ${impact_dollars:,.0f}"
                else:
                    impact_description = f"Decreases price by ${abs_impact:,.0f}"
            elif abs_impact > 25:
                if impact_dollars > 0:
                    impact_description = f"Small increase: +${impact_dollars:,.0f}"
                else:
                    impact_description = f"Small decrease: -${abs_impact:,.0f}"
            else:
                impact_description = f"${impact_dollars:+.0f}" if abs_impact > 1 else "Minimal impact"

            shap_contributions.append(ShapValueDataPoint(
                feature_name=feature_name.replace('_', ' ').title(),
                feature_value=feature_value,
                shap_value=float(shap_value),
                impact_description=impact_description
            ))

        # Sort by absolute SHAP value (most impactful first)
        shap_contributions.sort(key=lambda x: abs(x.shap_value), reverse=True)

        # Calculate confidence interval (simple approach using error model)
        error_prediction = loader.error_model.predict(df_for_prediction)[0]
        confidence_interval = {
            "lower": max(0, predicted_price - abs(np.expm1(error_prediction))),
            "upper": predicted_price + abs(np.expm1(error_prediction))
        }

        return PredictionExplanationResponse(
            prediction_input=input_data,
            predicted_price=predicted_price,
            base_price=float(np.expm1(base_value)) if base_value else 0,
            shap_contributions=shap_contributions,
            confidence_interval=confidence_interval,
            total_shap_sum=float(sum(feature_shap_values))
        )

    except Exception as e:
        print(f"Error getting prediction explanation: {e}")
        raise HTTPException(status_code=500, detail="Could not generate prediction explanation.")


@router.post("/load-main-model", tags=["Model Management"])
async def load_main_model(loader: ModelLoader = Depends(get_model_loader)):
    """Manually load the main model"""
    try:
        if hasattr(loader, 'load_main_model_manually'):
            success = loader.load_main_model_manually()
            if success:
                return {
                    "status": "success",
                    "message": "Main model loaded successfully",
                    "main_model_loaded": True
                }
            else:
                return {
                    "status": "error",
                    "message": "Failed to load main model",
                    "main_model_loaded": False
                }
        else:
            raise HTTPException(status_code=500, detail="Manual loading function not available")

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error loading main model: {e}")
        raise HTTPException(status_code=500, detail=f"Error loading main model: {str(e)}")


@router.post("/load-error-model", tags=["Model Management"])
async def load_error_model(loader: ModelLoader = Depends(get_model_loader)):
    """Manually load the error model"""
    try:
        if hasattr(loader, 'load_error_model_manually'):
            success = loader.load_error_model_manually()
            if success:
                return {
                    "status": "success",
                    "message": "Error model loaded successfully",
                    "error_model_loaded": True
                }
            else:
                return {
                    "status": "error",
                    "message": "Failed to load error model",
                    "error_model_loaded": False
                }
        else:
            raise HTTPException(status_code=500, detail="Manual loading function not available")

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error loading error model: {e}")
        raise HTTPException(status_code=500, detail=f"Error loading error model: {str(e)}")


@router.post("/load-both-models", tags=["Model Management"])
async def load_both_models(loader: ModelLoader = Depends(get_model_loader)):
    """Manually load both main and error models"""
    try:
        if hasattr(loader, 'load_both_models_manually'):
            success = loader.load_both_models_manually()
            return {
                "status": "success" if success else "partial",
                "message": "Model loading completed" if success else "Some models failed to load",
                "main_model_loaded": loader.main_model is not None,
                "error_model_loaded": loader.error_model is not None,
                "can_predict": loader.main_model is not None and loader.error_model is not None
            }
        else:
            raise HTTPException(status_code=500, detail="Manual loading function not available")

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error loading models: {e}")
        raise HTTPException(status_code=500, detail=f"Error loading models: {str(e)}")


@router.get("/model-status", tags=["Model Management"])
async def get_model_status(loader: ModelLoader = Depends(get_model_loader)):
    """Get the current status of loaded models"""
    try:
        return {
            "main_model_loaded": loader.main_model is not None,
            "error_model_loaded": loader.error_model is not None,
            "can_predict": loader.main_model is not None and loader.error_model is not None
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error getting model status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting model status: {str(e)}")