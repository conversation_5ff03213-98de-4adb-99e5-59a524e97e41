from pydantic import BaseModel, Field
from typing import List, Dict, Optional

# Pydantic models for API data validation

class PredictionInput(BaseModel):
    # 5 user inputs as per the plan
    make_name: str = Field(..., example="Ford")
    model_name: str = Field(..., example="F-150")
    # Plan table 1: cylinder_count is numeric (2.0, 3.0 ... 16.0)
    # Notebook has engine_cylinders (categorical I4, V6) and cylinder_count (numeric)
    # Assuming user provides numeric cylinder_count as per plan's table.
    cylinder_count: float = Field(..., example=6.0, description="Numeric value for number of cylinders (e.g., 4.0, 6.0, 8.0)")
    mileage: float = Field(..., example=50000, ge=0)
    year: int = Field(..., example=2018, ge=1900, le=2025) # Manufacturing year

    class Config:
        json_schema_extra = {
            "example": {
                "make_name": "Toyota",
                "model_name": "<PERSON><PERSON>",
                "cylinder_count": 4.0,
                "mileage": 60000,
                "year": 2019
            }
        }

class PredictionOutput(BaseModel):
    predicted_price: float = Field(..., example=25000.50)
    # Could add more details later, like confidence or feature importance if implemented

# Schemas for Individual Prediction Explanation
class ShapValueDataPoint(BaseModel):
    feature_name: str
    feature_value: str  # Human-readable value (e.g., "Toyota", "50,000 miles")
    shap_value: float   # SHAP contribution to prediction
    impact_description: str  # e.g., "Increases price by $2,500"

class PredictionExplanationResponse(BaseModel):
    prediction_input: PredictionInput
    predicted_price: float
    base_price: float  # Model's base prediction (intercept)
    shap_contributions: List[ShapValueDataPoint]
    confidence_interval: Optional[Dict[str, float]] = None  # {"lower": 20000, "upper": 30000}
    total_shap_sum: float  # Should equal predicted_price - base_price

class MakeModelResponse(BaseModel):
    makes: List[str] = Field(..., example=["Toyota", "Ford", "Honda"])
    models_by_make: Dict[str, List[str]] = Field(..., example={"Toyota": ["Camry", "Corolla"]})

class DropdownOptionsResponse(BaseModel):
    # This could be expanded to include ranges for numeric inputs if desired
    make_model_map: Dict[str, List[str]] = Field(..., example={"Toyota": ["Camry", "Corolla"]})
    cylinder_counts: List[float] = Field(..., example=[2.0, 3.0, 4.0, 5.0, 6.0, 8.0, 10.0, 12.0, 16.0])
    # year_range: Dict[str, int] = Field(..., example={"min": 1915, "max": 2021})
    # mileage_range: Dict[str, int] = Field(..., example={"min": 0, "max": 450000})

# Schemas for Price Distribution Visualization
class PriceDistributionParams(BaseModel):
    make_name: Optional[str] = None
    year_min: Optional[int] = None
    year_max: Optional[int] = None
    body_type: Optional[str] = None
    # Add bin_count or bin_width later if we want client to suggest it

class PriceDistributionDataPoint(BaseModel):
    price_bin_start: float # Lower edge of the price bin
    price_bin_end: float   # Upper edge of the price bin
    count: int             # Number of cars in this bin

class PriceDistributionResponse(BaseModel):
    data: List[PriceDistributionDataPoint]
    filters_applied: PriceDistributionParams
    # Could add summary_stats: Optional[Dict[str, float]] = None (e.g., mean, median)

# Schemas for 3D Scatter Plot Visualization
class Scatter3DParams(BaseModel):
    make_name: Optional[str] = None
    body_type: Optional[str] = None
    # Could add more filters like year_range if needed
    sample_size: int = Field(default=500, gt=0, le=2000) # Number of points to plot

class Scatter3DDataPoint(BaseModel):
    price: float
    mileage: float
    car_age: float
    make_name: Optional[str] = None # For coloring points by make, for example
    body_type: Optional[str] = None # For other categorizations

class Scatter3DResponse(BaseModel):
    data: List[Scatter3DDataPoint]
    filters_applied: Scatter3DParams
    x_axis_label: str = "Mileage"
    y_axis_label: str = "Car Age (Years)"
    z_axis_label: str = "Price ($)"

# Schemas for Feature Importance Visualization
class FeatureImportanceDataPoint(BaseModel):
    feature_name: str
    importance: float # Normalized or raw importance score

class FeatureImportanceResponse(BaseModel):
    data: List[FeatureImportanceDataPoint]
    model_type: str # e.g., "Main Model", "Error Model", or "Combined"
    top_n: Optional[int] = None