import os
from dotenv import load_dotenv
from pydantic_settings import BaseSettings

# Load .env file from the backend directory (where this config.py is, or its parent)
# It's good practice to place .env in the root of the backend or project and ensure it's in .gitignore
# For this structure, assuming .env might be in used_cars_saas/backend/
ENV_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
load_dotenv(ENV_FILE_PATH)

class Settings(BaseSettings):
    APP_NAME: str = "Used Car Price Prediction API"
    API_V1_STR: str = "/api/v1"
    PROJECT_VERSION: str = "0.1.0"

    # Model paths - can be overridden by .env file
    # These paths should be relative to the backend directory or absolute
    # For large models, these might point to locations outside the Docker image if volume mounted
    MAIN_MODEL_PATH: str = "models/catboost_main_model.cbm" # Example, adjust if needed
    ERROR_MODEL_PATH: str = "models/catboost_error_model.cbm" # Example, adjust if needed

    # Data paths for JSON files generated by data_preparation_utils.py
    # These are relative to the backend directory where the app runs
    MODEL_FEATURES_LIST_PATH: str = "model_features_list.json"
    FEATURE_DEFAULTS_PATH: str = "feature_defaults.json"
    MAKE_MODEL_MAP_PATH: str = "make_model_map.json"
    FEATURE_TYPES_PATH: str = "feature_types.json"
    FIXED_PREDICTION_PARAMS_PATH: str = "fixed_prediction_params.json"

    # Fixed listed_year for car_age calculation during prediction
    # This will be loaded from fixed_prediction_params.json by the model serving logic
    # but we can have a default here if that file was missing.
    DEFAULT_FIXED_LISTED_YEAR: int = 2021 

    # For development, if running main.py directly
    # Uvicorn settings if run programmatically (though usually run from CLI for production)
    # HOST: str = "0.0.0.0"
    # PORT: int = 8000

    class Config:
        case_sensitive = True
        # env_file = ".env" # pydantic-settings automatically loads if .env is in same dir as this script
                          # or if load_dotenv is used as above.

settings = Settings() 