import json
import os
import time
from catboost import CatBoostRegressor
from .config import settings # Use relative import for config within the same package
import pandas as pd
import numpy as np # Ensure numpy is imported

class ModelLoader:
    def __init__(self):
        self.main_model = None
        self.error_model = None
        self.model_features = []
        self.feature_defaults = {}
        self.make_model_map = {}
        self.feature_types = {"categorical": [], "numerical": []}
        self.fixed_prediction_params = {"fixed_listed_year_for_car_age": settings.DEFAULT_FIXED_LISTED_YEAR}
        
        self._load_models_and_data()

    def _load_json_data(self, file_path, description):
        try:
            # Correctly join with backend directory if paths in settings are relative to it
            # Assuming settings paths are relative to the backend root where main.py is
            full_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), file_path)
            if not os.path.exists(full_path):
                print(f"Warning: {description} file not found at {full_path}. Using empty/default.")
                return None if description != "Fixed prediction params" else self.fixed_prediction_params
            with open(full_path, 'r') as f:
                data = json.load(f)
            print(f"Successfully loaded {description} from {full_path}")
            return data
        except Exception as e:
            print(f"Error loading {description} from {file_path}: {e}. Using empty/default.")
            return None if description != "Fixed prediction params" else self.fixed_prediction_params

    def _load_catboost_model(self, model_path_setting, description):
        try:
            # model_path_setting is like "models/catboost_main_model.cbm"
            # This should be relative to the backend root directory
            full_model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), model_path_setting)
            print(f"Attempting to load {description} model from: {full_model_path}")
            print(f"File exists: {os.path.exists(full_model_path)}")

            if not os.path.exists(full_model_path):
                print(f"Critical Error: {description} model file not found at {full_model_path}. Model not loaded.")
                # Consider copying models from the root project directory if not found here
                # This logic might be added if models are not placed in backend/models directly
                project_root_model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), os.path.basename(model_path_setting))
                if os.path.exists(project_root_model_path):
                    print(f"Found {description} model in project root: {project_root_model_path}. Consider copying to {full_model_path} or adjusting paths.")
                # For now, we strictly expect them in the location specified by settings
                return None

            file_size_mb = os.path.getsize(full_model_path) / (1024 * 1024)
            print(f"File size: {file_size_mb:.1f} MB")
            print(f"Creating CatBoostRegressor instance...")
            model = CatBoostRegressor()
            print(f"Loading model from file (this may take a while for large models)...")

            # Enhanced model loading with detailed debugging
            start_time = time.time()
            try:
                print(f"⏳ Loading model ({file_size_mb:.1f} MB)...")
                print(f"📍 Model path: {full_model_path}")
                print(f"💾 Available system memory check...")

                # Check available memory before loading
                try:
                    import psutil
                    memory = psutil.virtual_memory()
                    available_gb = memory.available / (1024**3)
                    total_gb = memory.total / (1024**3)
                    used_gb = memory.used / (1024**3)
                    print(f"💾 Memory status: {used_gb:.1f}GB used / {total_gb:.1f}GB total ({available_gb:.1f}GB available)")

                    model_size_gb = file_size_mb / 1024
                    if available_gb < model_size_gb * 1.5:  # Need 1.5x model size for safe loading
                        print(f"⚠️  WARNING: Low memory! Model needs ~{model_size_gb:.1f}GB, only {available_gb:.1f}GB available")
                        print(f"⚠️  This may cause the system to crash or hang!")
                except ImportError:
                    print("💾 psutil not available - cannot check memory status")
                except Exception as e:
                    print(f"💾 Memory check failed: {e}")

                print(f"🔄 Starting CatBoost model loading...")
                print(f"🔄 This is where the crash typically occurs...")

                # Force garbage collection before loading
                import gc
                gc.collect()
                print(f"🧹 Garbage collection completed")

                # Try loading with memory monitoring
                print(f"🔄 Calling model.load_model()...")

                # Set up a signal handler to catch crashes (Windows)
                import signal
                import sys

                def signal_handler(signum, _):
                    print(f"💥 SIGNAL {signum} RECEIVED - SYSTEM IS KILLING THE PROCESS!")
                    print(f"💥 This indicates the OS killed us due to memory pressure")
                    sys.exit(1)

                # Register signal handlers
                signal.signal(signal.SIGTERM, signal_handler)
                signal.signal(signal.SIGINT, signal_handler)

                try:
                    # The actual loading call that crashes
                    model.load_model(full_model_path)
                    print(f"🎉 Model loading call completed successfully!")
                except Exception as e:
                    print(f"💥 Exception during model.load_model(): {e}")
                    raise

                load_time = time.time() - start_time
                print(f"✅ Successfully loaded {description} model in {load_time:.1f} seconds")

            except MemoryError as mem_error:
                load_time = time.time() - start_time
                print(f"💥 MEMORY ERROR after {load_time:.1f} seconds: {mem_error}")
                print(f"💡 The model ({file_size_mb:.1f} MB) is too large for available memory!")
                print(f"💡 Try closing other applications or use a machine with more RAM")
                raise mem_error
            except OSError as os_error:
                load_time = time.time() - start_time
                print(f"💥 OS ERROR after {load_time:.1f} seconds: {os_error}")
                print(f"💡 This could be a file access issue or disk space problem")
                raise os_error
            except Exception as load_error:
                load_time = time.time() - start_time
                print(f"❌ UNKNOWN ERROR after {load_time:.1f} seconds: {load_error}")
                print(f"❌ Error type: {type(load_error).__name__}")
                print(f"❌ Full error details:")
                import traceback
                traceback.print_exc()
                raise load_error

            # Try to get feature count (different CatBoost versions have different attributes)
            try:
                if hasattr(model, 'feature_count_'):
                    print(f"Model feature count: {model.feature_count_}")
                elif hasattr(model, 'get_feature_count'):
                    print(f"Model feature count: {model.get_feature_count()}")
                else:
                    print("Model loaded successfully (feature count not available)")
            except Exception as e:
                print(f"Could not get feature count: {e}")

            return model
        except Exception as e:
            print(f"Critical Error loading {description} model from {model_path_setting}: {e}. Model not loaded.")
            import traceback
            traceback.print_exc()
            return None

    def _load_large_model_with_retry(self, model_path_setting, description, max_retries=3):
        """Enhanced loading for large models with retry mechanism and memory optimization"""
        import gc
        import time

        for attempt in range(max_retries):
            try:
                print(f"🔄 Attempt {attempt + 1}/{max_retries} to load {description} model...")

                # Force garbage collection before loading
                gc.collect()

                # Show memory status
                try:
                    import psutil
                    memory = psutil.virtual_memory()
                    print(f"💾 Available RAM: {memory.available / (1024**3):.1f} GB / {memory.total / (1024**3):.1f} GB")
                except:
                    pass

                # Try loading with shorter timeout on first attempts
                timeout_minutes = 5 + (attempt * 5)  # 5, 10, 15 minutes
                print(f"⏰ Using {timeout_minutes} minute timeout for this attempt")

                model = self._load_catboost_model_with_timeout(model_path_setting, description, timeout_minutes)

                if model is not None:
                    print(f"✅ Successfully loaded {description} model on attempt {attempt + 1}")
                    return model
                else:
                    print(f"❌ Failed to load {description} model on attempt {attempt + 1}")

            except Exception as e:
                print(f"❌ Error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # Exponential backoff: 1, 2, 4 seconds
                    print(f"⏳ Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)

        print(f"❌ Failed to load {description} model after {max_retries} attempts")
        return None

    def _load_catboost_model_with_timeout(self, model_path_setting, description, timeout_minutes=15):
        """Load CatBoost model with configurable timeout"""
        try:
            full_model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), model_path_setting)
            print(f"Loading {description} model from: {full_model_path}")

            if not os.path.exists(full_model_path):
                print(f"Model file not found: {full_model_path}")
                return None

            file_size_mb = os.path.getsize(full_model_path) / (1024 * 1024)
            print(f"File size: {file_size_mb:.1f} MB")

            model = CatBoostRegressor()

            # Use concurrent loading with timeout
            import concurrent.futures

            def load_model():
                model.load_model(full_model_path)
                return model

            timeout_seconds = timeout_minutes * 60

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(load_model)
                try:
                    start_time = time.time()
                    model = future.result(timeout=timeout_seconds)
                    load_time = time.time() - start_time
                    print(f"✅ Model loaded successfully in {load_time:.1f} seconds")
                    return model
                except concurrent.futures.TimeoutError:
                    print(f"⏰ Model loading timed out after {timeout_minutes} minutes")
                    return None

        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return None

    def _load_models_and_data(self):
        print("Initializing ModelLoader...")
        print(f"Current working directory: {os.getcwd()}")
        print(f"Model paths - Main: {settings.MAIN_MODEL_PATH}, Error: {settings.ERROR_MODEL_PATH}")

        # System information for debugging
        print("\n🖥️  SYSTEM INFORMATION:")
        try:
            import psutil
            import platform

            # System info
            print(f"🖥️  OS: {platform.system()} {platform.release()}")
            print(f"🖥️  Python: {platform.python_version()}")

            # Memory info
            memory = psutil.virtual_memory()
            print(f"💾 Total RAM: {memory.total / (1024**3):.1f} GB")
            print(f"💾 Available RAM: {memory.available / (1024**3):.1f} GB")
            print(f"💾 Used RAM: {memory.used / (1024**3):.1f} GB ({memory.percent:.1f}%)")

            # Disk info
            disk = psutil.disk_usage(os.getcwd())
            print(f"💽 Disk space: {disk.free / (1024**3):.1f} GB free / {disk.total / (1024**3):.1f} GB total")

        except ImportError:
            print("⚠️  psutil not available - install with: pip install psutil")
        except Exception as e:
            print(f"⚠️  Could not get system info: {e}")
        print("=" * 60)

        # Load JSON data first (these are less likely to cause issues)
        print("Loading JSON configuration files...")
        self.model_features = self._load_json_data(settings.MODEL_FEATURES_LIST_PATH, "Model features list") or []
        self.feature_defaults = self._load_json_data(settings.FEATURE_DEFAULTS_PATH, "Feature defaults") or {}
        self.make_model_map = self._load_json_data(settings.MAKE_MODEL_MAP_PATH, "Make-model map") or {}
        self.feature_types = self._load_json_data(settings.FEATURE_TYPES_PATH, "Feature types") or {"categorical": [], "numerical": []}

        loaded_fixed_params = self._load_json_data(settings.FIXED_PREDICTION_PARAMS_PATH, "Fixed prediction params")
        if loaded_fixed_params: # Ensure it's not None and update
             self.fixed_prediction_params.update(loaded_fixed_params)

        print("JSON files loaded successfully. Now loading CatBoost models...")

        # Simple model loading strategy - load all models without filters
        print("Attempting to load all models without size restrictions...")

        try:
            print("Loading main CatBoost model...")
            main_model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), settings.MAIN_MODEL_PATH)
            if os.path.exists(main_model_path):
                size_gb = os.path.getsize(main_model_path) / (1024**3)
                print(f"Main model size: {size_gb:.1f} GB")
                print(f"Loading main model...")

                # Try different loading approaches for large models
                if size_gb > 10:  # Very large model
                    print(f"🔄 Attempting optimized loading for very large model...")

                    # Approach 1: Try with explicit memory management
                    try:
                        import gc
                        gc.collect()  # Clear memory first

                        print(f"🔄 Creating CatBoostRegressor with minimal memory footprint...")
                        model = CatBoostRegressor()

                        print(f"🔄 Loading model with explicit path...")
                        full_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), settings.MAIN_MODEL_PATH)

                        # Monitor memory during loading
                        try:
                            import psutil
                            process = psutil.Process()
                            mem_before = process.memory_info().rss / (1024**3)
                            print(f"💾 Process memory before: {mem_before:.2f} GB")
                        except:
                            pass

                        # The critical loading call
                        model.load_model(full_path)

                        try:
                            mem_after = process.memory_info().rss / (1024**3)
                            print(f"💾 Process memory after: {mem_after:.2f} GB")
                            print(f"💾 Memory increase: {mem_after - mem_before:.2f} GB")
                        except:
                            pass

                        self.main_model = model
                        print(f"✅ Large model loaded successfully with optimized approach!")

                    except Exception as e:
                        print(f"❌ Optimized loading failed: {e}")
                        print(f"🔄 Falling back to standard loading...")
                        self.main_model = self._load_catboost_model(settings.MAIN_MODEL_PATH, "Main CatBoost")
                else:
                    # Standard loading for smaller models
                    self.main_model = self._load_catboost_model(settings.MAIN_MODEL_PATH, "Main CatBoost")
            else:
                print("Main model file not found.")
                self.main_model = None
            print(f"Main model loaded: {self.main_model is not None}")
        except Exception as e:
            print(f"Failed to load main model: {e}")
            import traceback
            traceback.print_exc()
            self.main_model = None

        try:
            print("Loading error CatBoost model...")
            error_model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), settings.ERROR_MODEL_PATH)
            if os.path.exists(error_model_path):
                size_gb = os.path.getsize(error_model_path) / (1024**3)
                print(f"Error model size: {size_gb:.1f} GB")
                print(f"Loading error model...")
                self.error_model = self._load_catboost_model(settings.ERROR_MODEL_PATH, "Error CatBoost")
            else:
                print("Error model file not found.")
                self.error_model = None
            print(f"Error model loaded: {self.error_model is not None}")
        except Exception as e:
            print(f"Failed to load error model: {e}")
            import traceback
            traceback.print_exc()
            self.error_model = None

        print(f"ModelLoader initialization complete. Main model loaded: {self.main_model is not None}, Error model loaded: {self.error_model is not None}")
        print(f"JSON data loaded - Features: {len(self.model_features)}, Defaults: {len(self.feature_defaults)}, Make-model map: {len(self.make_model_map)}")

    def get_prediction_data_dict(self, user_inputs: dict):
        """
        Constructs the full feature dictionary for prediction based on user inputs,
        applies defaults, and engineers features like car_age and mileage_per_age.
        """
        if not self.model_features:
            raise ValueError("Model features list not loaded. Cannot prepare data.")

        # Start with a copy of defaults
        data_for_prediction = self.feature_defaults.copy()

        # Override with user inputs
        for key, value in user_inputs.items():
            if key in self.model_features:
                data_for_prediction[key] = value
            # else: # Optional: warn about unexpected user inputs
            #     print(f"Warning: User input '{key}' is not in model features list.")

        # Engineer features
        # car_age calculation
        mfg_year = user_inputs.get('year')
        if mfg_year is None:
            raise ValueError("'year' (manufacturing year) must be provided by the user.")
        try:
            mfg_year = int(mfg_year)
        except ValueError:
            raise ValueError("'year' must be a valid integer.")
        
        fixed_listed_year = self.fixed_prediction_params.get('fixed_listed_year_for_car_age', settings.DEFAULT_FIXED_LISTED_YEAR)
        car_age = fixed_listed_year - mfg_year
        data_for_prediction['car_age'] = car_age

        # mileage_per_age calculation
        mileage = user_inputs.get('mileage')
        if mileage is None:
            raise ValueError("'mileage' must be provided by the user.")
        try:
            mileage = float(mileage)
        except ValueError:
            raise ValueError("'mileage' must be a valid number.")

        data_for_prediction['mileage_per_age'] = mileage / (car_age + 0.01) # Avoid division by zero
        
        # Ensure all features expected by the model are present, in correct order for DataFrame construction later
        final_feature_dict = {feature: data_for_prediction.get(feature) for feature in self.model_features}
        
        # CatBoost needs categorical features as str or float/int. Pydantic might give them as correct types.
        # Ensure categorical features are stringified if not already appropriate type for CatBoost.
        # The data_preparation_utils.py should have handled NaN -> "__NAN__" for training data strings.
        # For defaults, this is already handled by data_preparation_utils.py saving them as strings.
        # For user inputs, this might need explicit casting in the API endpoint if pydantic model isn't stringifying.
        for cat_feature in self.feature_types.get("categorical", []):
            if cat_feature in final_feature_dict and final_feature_dict[cat_feature] is not None:
                 # Check if it's a type that CatBoost might not like for a categorical (e.g. raw int that should be string category)
                 # For now, assume Pydantic models + JSON defaults handle this well. Explicit casting can be added.
                pass #final_feature_dict[cat_feature] = str(final_feature_dict[cat_feature])

        return final_feature_dict
    
    def predict(self, user_inputs: dict):
        if not self.main_model or not self.error_model:
            raise RuntimeError("Models are not loaded. Cannot predict.")

        feature_dict = self.get_prediction_data_dict(user_inputs)
        
        # Convert to DataFrame in the order of model_features
        # This ensures the column order matches training
        df_for_prediction = pd.DataFrame([feature_dict], columns=self.model_features)

        # Ensure dtypes are consistent with training, especially for categoricals handled as strings
        for col in self.feature_types.get("categorical", []):
            if col in df_for_prediction.columns:
                df_for_prediction[col] = df_for_prediction[col].astype(str).fillna('__NAN__')
        # Note: Numerical features should already be of appropriate types from Pydantic/defaults.

        # Predict with main model
        y_pred_log_cb = self.main_model.predict(df_for_prediction)
        
        # Predict with error model
        predicted_log_errors = self.error_model.predict(df_for_prediction)
        
        # Combine predictions
        final_y_pred_log = y_pred_log_cb + predicted_log_errors
        
        # Transform to original scale and ensure non-negative
        final_price = np.expm1(final_y_pred_log)
        final_price[final_price < 0] = 0
        
        return final_price[0] # Return single price value

    def load_main_model_manually(self):
        """Manually load the main model - useful for large models"""
        if self.main_model is not None:
            print("Main model is already loaded.")
            return True

        try:
            print("Manually loading main CatBoost model...")
            main_model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), settings.MAIN_MODEL_PATH)
            if os.path.exists(main_model_path):
                size_gb = os.path.getsize(main_model_path) / (1024**3)
                print(f"Main model size: {size_gb:.1f} GB")
                print("This may take several minutes for large models...")

                self.main_model = self._load_catboost_model(settings.MAIN_MODEL_PATH, "Main CatBoost")
                if self.main_model is not None:
                    print("✅ Main model loaded successfully!")
                    return True
                else:
                    print("❌ Failed to load main model")
                    return False
            else:
                print(f"❌ Main model file not found: {main_model_path}")
                return False
        except Exception as e:
            print(f"❌ Error manually loading main model: {e}")
            return False

    def load_error_model_manually(self):
        """Manually load the error model - useful for large models"""
        if self.error_model is not None:
            print("Error model is already loaded.")
            return True

        try:
            print("Manually loading error CatBoost model...")
            error_model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), settings.ERROR_MODEL_PATH)
            if os.path.exists(error_model_path):
                size_gb = os.path.getsize(error_model_path) / (1024**3)
                print(f"Error model size: {size_gb:.1f} GB")
                print("This may take several minutes for large models...")

                self.error_model = self._load_catboost_model_with_timeout(settings.ERROR_MODEL_PATH, "Error CatBoost", timeout_minutes=15)
                if self.error_model is not None:
                    print("✅ Error model loaded successfully!")
                    return True
                else:
                    print("❌ Failed to load error model")
                    return False
            else:
                print(f"❌ Error model file not found: {error_model_path}")
                return False
        except Exception as e:
            print(f"❌ Error manually loading error model: {e}")
            return False

    def load_both_models_manually(self):
        """Load both models manually with progress tracking"""
        print("🚀 Starting manual loading of both models...")

        main_success = self.load_main_model_manually()
        error_success = self.load_error_model_manually()

        if main_success and error_success:
            print("✅ Both models loaded successfully! Prediction service is now fully operational.")
            return True
        elif main_success:
            print("⚠️ Main model loaded, but error model failed. Limited functionality available.")
            return False
        elif error_success:
            print("⚠️ Error model loaded, but main model failed. Cannot make predictions.")
            return False
        else:
            print("❌ Both models failed to load.")
            return False

# Singleton instance of the model loader
# This ensures models are loaded only once when the application starts importing this module.
# For large models (>10GB), we'll initialize without loading models immediately
try:
    print("Creating ModelLoader instance...")
    model_loader_instance = ModelLoader()
    print("ModelLoader initialized successfully!")
    if model_loader_instance.main_model is None or model_loader_instance.error_model is None:
        print("Warning: Some models failed to load. The application will run with limited functionality.")
        print("Consider using a smaller model or increasing available memory.")
except Exception as e:
    print(f"Warning: Failed to initialize ModelLoader: {e}")
    print("Creating a minimal ModelLoader instance for basic functionality...")
    # Create a minimal instance that can at least provide JSON data
    model_loader_instance = type('ModelLoader', (), {
        'main_model': None,
        'error_model': None,
        'model_features': [],
        'feature_defaults': {},
        'make_model_map': {},
        'feature_types': {"categorical": [], "numerical": []},
        'fixed_prediction_params': {"fixed_listed_year_for_car_age": 2021}
    })()