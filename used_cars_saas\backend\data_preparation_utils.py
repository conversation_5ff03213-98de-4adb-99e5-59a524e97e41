import pandas as pd
import numpy as np
import json

# --- Configuration ---
PARQUET_FILE_PATH = '../../cars_df.parquet' # Relative to this script's location in backend/
OUTPUT_DIR = '.' # Output files in the same directory as script

# User-provided features on the predictor page (as per Website Plan PDF)
USER_INPUT_FEATURES = ['make_name', 'model_name', 'cylinder_count', 'mileage', 'year']

# Date part features that are part of the 40 features but from which car_age is derived
# These themselves don't need defaults filled by this script's logic, as car_age (from user 'year') is the key input.
# However, they are part of the full feature vector the model expects.
DATE_PART_FEATURES = ['listed_year', 'listed_month', 'listed_day', 'listed_dayofweek']

# Features engineered from user inputs or fixed values, not needing direct default calculation from dataset stats
ENGINEERED_FEATURES_FROM_INPUTS = ['car_age', 'mileage_per_age']


def get_feature_info(df):
    print(f"Loading DataFrame from {PARQUET_FILE_PATH}...")
    if df is None:
        print(f"Failed to load DataFrame from {PARQUET_FILE_PATH}")
        return None, None, None, None

    print(f"DataFrame loaded. Shape: {df.shape}")

    # Define features for the model (all columns except 'price')
    if 'price' in df.columns:
        model_features = df.drop(columns=['price']).columns.tolist()
    else:
        model_features = df.columns.tolist()
    
    print(f"Total features for model (X.columns): {len(model_features)}")
    print("First 10 model features:", model_features[:10])
    
    # Identify categorical and numerical features
    categorical_features = df[model_features].select_dtypes(include=['object', 'category']).columns.tolist()
    numerical_features = df[model_features].select_dtypes(include=[np.number]).columns.tolist()

    print(f"Identified {len(categorical_features)} categorical features.")
    print(f"Identified {len(numerical_features)} numerical features.")

    # Features needing default values:
    # These are features in model_features that are NOT in USER_INPUT_FEATURES 
    # AND NOT in DATE_PART_FEATURES (as these are handled by car_age derivation)
    # AND NOT in ENGINEERED_FEATURES_FROM_INPUTS (as these are derived)
    
    # Correctly identify features for which defaults need to be calculated
    # The model was trained on X which included listed_year, listed_month etc.
    # and car_age, mileage_per_age were also part of X.
    # For prediction:
    # - User provides 5 core inputs.
    # - car_age is made from user_input_year and fixed_listed_year (e.g. 2021)
    # - mileage_per_age from user_mileage and calculated car_age
    # - The *original* listed_year, listed_month etc. from the dataset will need defaults
    #   if they were part of the model's training features AND are not the ones being
    #   used to derive car_age for prediction (which uses a *fixed* listed_year).
    #
    # The `Website Development Plan` (Table 2) lists features like `listed_day`, `listed_month`, `listed_dayofweek`
    # as needing default values. This implies the model *was* trained on these original date parts.
    # `car_age` and `mileage_per_age` were also explicitly in the training data.
    #
    # When predicting, we create `car_age` and `mileage_per_age` on the fly.
    # The other original features from the training set need defaults.

    features_for_defaults = [
        f for f in model_features 
        if f not in USER_INPUT_FEATURES + ENGINEERED_FEATURES_FROM_INPUTS
    ]
    # If 'year' (mfg_year) from user input is different from 'listed_year' from training data,
    # then original 'listed_year', 'listed_month', 'listed_day', 'listed_dayofweek'
    # might still need defaults if they were part of the training features directly.
    # Table 2 in the plan suggests 'listed_day', 'listed_month', 'listed_dayofweek' get defaults.
    # 'car_age' itself is also in Table 2 as needing a default - this would be the case
    # if the user *didn't* provide 'year'. But our predictor *requires* 'year'.
    # So, 'car_age' and 'mileage_per_age' will always be calculated.

    # The critical distinction: what features did the *saved CatBoost model file* learn on?
    # The notebook indicates X_train had 40 columns. These are the ones we need to reconstruct.
    # These 40 columns included 'car_age', 'mileage_per_age', 'listed_year', 'listed_month', etc.

    # Let's assume for now the defaults are needed for features in model_features
    # that are NOT the 5 user inputs AND NOT the 2 directly engineered ones.
    # The DATE_PART_FEATURES from the original dataset will need defaults if they
    # are not directly derivable from user input in the same way as 'car_age'.
    # For example, if the model expects 'listed_month' from the original data,
    # and the user only gives 'year' (mfg_year), 'listed_month' needs a default.

    print(f"Features considered for default value calculation: {len(features_for_defaults)}")

    default_values = {}
    for feature in features_for_defaults:
        if feature in df.columns: # Ensure feature exists in DataFrame
            if feature in categorical_features:
                default_values[feature] = df[feature].mode()[0] if not df[feature].mode().empty else '__NAN__'
            elif feature in numerical_features:
                default_values[feature] = df[feature].median()
            else: # Should not happen if correctly identified
                print(f"Warning: Feature {feature} not found in categorical or numerical lists.")
        else:
            print(f"Warning: Feature {feature} for default calculation not found in DataFrame columns.")


    print(f"Calculated {len(default_values)} default values.")
    
    # Make and Model lists
    make_model_map = {}
    if 'make_name' in df.columns and 'model_name' in df.columns:
        unique_makes = df['make_name'].unique().tolist()
        for make in unique_makes:
            if pd.isna(make): # Handle potential NaN makes
                models = df[df['make_name'].isna()]['model_name'].unique().tolist()
            else:
                models = df[df['make_name'] == make]['model_name'].unique().tolist()
            make_model_map[make if pd.notna(make) else '_NAN_MAKE_'] = [m if pd.notna(m) else '_NAN_MODEL_' for m in models]
        print(f"Extracted {len(unique_makes)} unique makes and their models.")
    else:
        print("Warning: 'make_name' or 'model_name' not in DataFrame. Cannot extract make/model map.")
        
    return model_features, default_values, make_model_map, categorical_features, numerical_features

def save_to_json(data, filename, directory):
    filepath = f"{directory}/{filename}"
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=4, default=str) # Use default=str to handle non-serializable types like numpy's
    print(f"Successfully saved {filename} to {filepath}")

if __name__ == '__main__':
    try:
        main_df = pd.read_parquet(PARQUET_FILE_PATH)
    except Exception as e:
        print(f"Error loading parquet file {PARQUET_FILE_PATH}: {e}")
        main_df = None

    if main_df is not None:
        # Calculate car_age and mileage_per_age as they would be during training
        # This assumes 'listed_date' and 'year' (mfg year) columns exist
        # For the purpose of getting the full feature list that the model was trained on.
        if 'listed_date' in main_df.columns and 'year' in main_df.columns:
            main_df['listed_date'] = pd.to_datetime(main_df['listed_date'], errors='coerce')
            main_df['listed_year_actual'] = main_df['listed_date'].dt.year # The actual listed_year from data
            
            # car_age calculation as done in notebook (using actual listed_year from data for this step)
            main_df['car_age'] = main_df['listed_year_actual'] - main_df['year']
            main_df['mileage_per_age'] = main_df['mileage'] / (main_df['car_age'] + 0.01) # Avoid division by zero
            
            # Add date parts if not already present (notebook cell 135)
            if 'listed_year' not in main_df.columns: main_df['listed_year'] = main_df['listed_date'].dt.year
            if 'listed_month' not in main_df.columns: main_df['listed_month'] = main_df['listed_date'].dt.month
            if 'listed_day' not in main_df.columns: main_df['listed_day'] = main_df['listed_date'].dt.day
            if 'listed_dayofweek' not in main_df.columns: main_df['listed_dayofweek'] = main_df['listed_date'].dt.dayofweek
            print("Ensured 'car_age', 'mileage_per_age', and date part features are present for feature listing.")

        else:
            print("Warning: 'listed_date' or 'year' not in DataFrame. Cannot create 'car_age' or 'mileage_per_age' for accurate feature list extraction.")


        # Re-run get_feature_info with the DataFrame that now surely includes these engineered features
        all_model_features, calculated_defaults, extracted_make_model_map, categorical_list, numerical_list = get_feature_info(main_df)

        if all_model_features:
            save_to_json(all_model_features, 'model_features_list.json', OUTPUT_DIR)
            # Save feature types
            feature_types = {'categorical': categorical_list, 'numerical': numerical_list}
            save_to_json(feature_types, 'feature_types.json', OUTPUT_DIR)

        if calculated_defaults:
            save_to_json(calculated_defaults, 'feature_defaults.json', OUTPUT_DIR)
        if extracted_make_model_map:
            save_to_json(extracted_make_model_map, 'make_model_map.json', OUTPUT_DIR)
        
        # For `listed_year` to be used in `car_age` calculation for *prediction*
        # The plan suggests using 2021 or current year. Let's document 2021.
        fixed_prediction_params = {'fixed_listed_year_for_car_age': 2021}
        save_to_json(fixed_prediction_params, 'fixed_prediction_params.json', OUTPUT_DIR)

        print("\n--- Summary of User Input Features (for predictor page) ---")
        print(USER_INPUT_FEATURES)

        print("\n--- Example of how car_age and mileage_per_age will be engineered for prediction ---")
        print("car_age = fixed_listed_year_for_car_age (e.g., 2021) - user_input_year")
        print("mileage_per_age = user_input_mileage / (calculated_car_age + 0.01)")

        print("\nData collation script finished.")
    else:
        print("Data collation script could not run due to DataFrame loading issues.") 