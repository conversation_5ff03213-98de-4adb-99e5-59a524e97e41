from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware # Import CORS middleware
from core.config import settings # Absolute import
from api import predict # Absolute import for the router

app = FastAPI(
    title=settings.APP_NAME,
    description="API to predict used car prices and serve related data.",
    version=settings.PROJECT_VERSION
)

# CORS Middleware Configuration
# Allow all origins for local development. For production, restrict this.
origins = [
    "http://localhost",         # General localhost
    "http://localhost:3000",    # Default React dev port
    # Add any other origins if your frontend might be served from a different port/URL
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # List of origins that are allowed to make requests
    allow_credentials=True, # Allow cookies to be included in requests
    allow_methods=["*"],    # Allow all methods (GET, POST, etc.)
    allow_headers=["*"],    # Allow all headers
)

# Mount the prediction API router
app.include_router(predict.router, prefix=settings.API_V1_STR, tags=["Prediction Service"])

@app.get("/health", tags=["Health"])
async def health_check():
    """Check the health of the application."""
    # model_loader_instance is loaded when core.model_loader is imported (e.g. by api.predict)
    # A more robust health check could verify model_loader_instance.main_model is not None
    return {"status": "ok", "message": "API is running."}

# Placeholder for future API routers
# from api import prediction_router, data_router # Example
# app.include_router(prediction_router.router)
# app.include_router(data_router.router)

if __name__ == "__main__":
    import uvicorn
    # This is for local development running directly with `python main.py`
    # For production, Uvicorn should be run as a separate process
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) # Added reload=True for dev