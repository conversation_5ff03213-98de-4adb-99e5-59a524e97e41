#!/usr/bin/env python3
"""
Standalone model loading test to diagnose the crash issue
"""

import os
import sys
import time
import gc
import psutil
from catboost import CatBoostRegressor

def check_system_info():
    """Display system information"""
    print("=" * 60)
    print("🖥️  SYSTEM DIAGNOSTIC")
    print("=" * 60)
    
    # Memory info
    memory = psutil.virtual_memory()
    print(f"💾 Total RAM: {memory.total / (1024**3):.1f} GB")
    print(f"💾 Available RAM: {memory.available / (1024**3):.1f} GB")
    print(f"💾 Used RAM: {memory.used / (1024**3):.1f} GB ({memory.percent:.1f}%)")
    
    # Process info
    process = psutil.Process()
    print(f"🔧 Process PID: {process.pid}")
    print(f"🔧 Process memory: {process.memory_info().rss / (1024**3):.2f} GB")
    
    return memory.available / (1024**3)

def test_model_loading():
    """Test loading the large model with detailed monitoring"""
    
    model_path = "models/catboost_main_model.cbm"
    
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        return False
    
    file_size_gb = os.path.getsize(model_path) / (1024**3)
    print(f"📊 Model file size: {file_size_gb:.1f} GB")
    
    available_gb = check_system_info()
    
    if available_gb < file_size_gb * 2:
        print(f"⚠️  WARNING: Potentially insufficient memory!")
        print(f"⚠️  Model needs ~{file_size_gb * 2:.1f}GB, only {available_gb:.1f}GB available")
    
    print("\n" + "=" * 60)
    print("🔄 STARTING MODEL LOADING TEST")
    print("=" * 60)
    
    # Force garbage collection
    print("🧹 Running garbage collection...")
    gc.collect()
    
    # Create model instance
    print("🔧 Creating CatBoostRegressor instance...")
    model = CatBoostRegressor()
    
    # Monitor memory before loading
    process = psutil.Process()
    memory_before = process.memory_info().rss / (1024**3)
    print(f"💾 Process memory before loading: {memory_before:.2f} GB")
    
    print(f"🔄 Starting model.load_model() call...")
    print(f"📍 Path: {os.path.abspath(model_path)}")
    
    start_time = time.time()
    
    try:
        # This is where the crash happens
        model.load_model(model_path)
        
        load_time = time.time() - start_time
        memory_after = process.memory_info().rss / (1024**3)
        
        print(f"✅ SUCCESS! Model loaded in {load_time:.1f} seconds")
        print(f"💾 Process memory after loading: {memory_after:.2f} GB")
        print(f"💾 Memory increase: {memory_after - memory_before:.2f} GB")
        
        return True
        
    except MemoryError as e:
        load_time = time.time() - start_time
        print(f"💥 MEMORY ERROR after {load_time:.1f} seconds: {e}")
        return False
        
    except Exception as e:
        load_time = time.time() - start_time
        print(f"❌ ERROR after {load_time:.1f} seconds: {e}")
        print(f"❌ Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 CatBoost Large Model Loading Test")
    print(f"🐍 Python version: {sys.version}")
    
    try:
        import catboost
        print(f"📦 CatBoost version: {catboost.__version__}")
    except:
        print("❌ Could not get CatBoost version")
    
    # Test 1: System check
    available_gb = check_system_info()
    
    # Test 2: Model loading
    success = test_model_loading()
    
    if success:
        print("\n🎉 MODEL LOADING SUCCESSFUL!")
        print("The issue may be related to the FastAPI/ModelLoader context")
    else:
        print("\n💥 MODEL LOADING FAILED!")
        print("This confirms the model loading issue")
    
    print("\n" + "=" * 60)
    print("🔍 DIAGNOSTIC COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
