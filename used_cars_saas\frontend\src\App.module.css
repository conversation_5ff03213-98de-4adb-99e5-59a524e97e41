.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.appHeader {
  background: linear-gradient(135deg,
    rgba(30, 30, 35, 0.95) 0%,
    rgba(30, 30, 35, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-xl);
  padding: var(--space-8) var(--space-6);
  position: relative;
  overflow: hidden;
}

.appHeader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-rainbow);
  opacity: 0.1;
  z-index: 0;
}

.appHeader::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-rainbow);
  z-index: 1;
}

.appHeader h1 {
  margin: 0;
  font-size: var(--text-4xl);
  font-weight: 900;
  text-align: center;
  position: relative;
  z-index: 2;
  background: var(--gradient-rainbow);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
}

.nav {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-top: var(--space-6);
  position: relative;
  z-index: 2;
}

.navLink {
  text-decoration: none;
  color: var(--gray-700);
  font-weight: 600;
  font-size: var(--text-base);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-2xl);
  transition: var(--transition-spring);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.navLink::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  transition: var(--transition-spring);
  z-index: -1;
}

.navLink:hover::before {
  left: 0;
}

.navLink:hover {
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary);
  border-color: var(--primary-300);
}

.navLinkActive {
  background: var(--gradient-primary);
  color: var(--white);
  box-shadow: var(--shadow-primary);
  border-color: var(--primary-300);
  transform: translateY(-2px);
}

.navLinkActive::before {
  left: 0;
}

.mainContent {
  flex-grow: 1;
  padding: var(--space-8);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.footer {
  padding: var(--space-6);
  background: var(--white);
  border-top: 1px solid var(--gray-200);
  text-align: center;
  font-size: var(--text-sm);
  color: var(--gray-500);
  margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .appHeader h1 {
    font-size: var(--text-3xl);
  }

  .nav {
    flex-direction: column;
    gap: var(--space-2);
    padding: var(--space-3);
  }

  .navLink {
    padding: var(--space-3) var(--space-4);
    text-align: center;
  }

  .mainContent {
    padding: var(--space-6) var(--space-3);
  }
}

@media (max-width: 480px) {
  .appHeader {
    padding: var(--space-4) var(--space-3);
  }

  .appHeader h1 {
    font-size: var(--text-2xl);
  }

  .mainContent {
    padding: var(--space-4) var(--space-2);
  }
}