import React from 'react';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  NavLink
} from 'react-router-dom';
import styles from './App.module.css'; // Import CSS Module
import HomePage from './components/HomePage'; // Import HomePage
import PredictorPage from './pages/PredictorPage'; // Import the new page component

function App() {
  return (
    <Router>
      <div className={styles.app}>
        <header className={styles.appHeader}>
          <h1>US Used Cars</h1>
          <nav className={styles.nav}>
            <NavLink
              to="/"
              className={({ isActive }) => isActive ? `${styles.navLink} ${styles.navLinkActive}` : styles.navLink}
            >
              Home
            </NavLink>
            <NavLink
              to="/predictor"
              className={({ isActive }) => isActive ? `${styles.navLink} ${styles.navLinkActive}` : styles.navLink}
            >
              Price Predictor
            </NavLink>
          </nav>
        </header>
        <main className={styles.mainContent}>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/predictor" element={<PredictorPage />} />
          </Routes>
        </main>
        <footer className={styles.footer}>
          <p>&copy; {new Date().getFullYear()} US Used Cars. All rights reserved.</p>
        </footer>
      </div>
    </Router>
  );
}

export default App;
