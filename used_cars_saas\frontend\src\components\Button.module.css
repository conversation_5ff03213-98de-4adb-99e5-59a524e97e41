.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--radius-xl);
  font-family: var(--font-family-primary);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-bounce);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  box-shadow: var(--shadow-md);
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-smooth);
}

.button:hover::before {
  left: 100%;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

/* Variants */
.primary {
  background: var(--gradient-primary);
  color: white;
}

.secondary {
  background: var(--gradient-secondary);
  color: white;
}

.accent {
  background: var(--gradient-accent);
  color: white;
}

.success {
  background: var(--gradient-success);
  color: white;
}

.warning {
  background: var(--gradient-warning);
  color: white;
}

.error {
  background: var(--gradient-error);
  color: white;
}

.ghost {
  background: transparent;
  color: var(--primary-600);
  box-shadow: none;
}

.ghost:hover {
  background: var(--primary-50);
  box-shadow: var(--shadow-sm);
}

.outline {
  background: transparent;
  color: var(--primary-600);
  border: 2px solid var(--primary-500);
  box-shadow: none;
}

.outline:hover {
  background: var(--primary-500);
  color: white;
}

/* Sizes */
.small {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  gap: var(--space-1);
}

.medium {
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  gap: var(--space-2);
}

.large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  gap: var(--space-3);
}

/* States */
.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: var(--shadow-sm) !important;
}

.disabled::before {
  display: none;
}

.fullWidth {
  width: 100%;
}

/* Content styling */
.loadingContent {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.contentWithIcon {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.icon {
  display: flex;
  align-items: center;
  font-size: 1.2em;
}

/* Responsive */
@media (max-width: 768px) {
  .large {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
  }
  
  .medium {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
  }
}
