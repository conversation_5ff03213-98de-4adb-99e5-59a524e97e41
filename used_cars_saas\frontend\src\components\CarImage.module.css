.container {
  width: 100%;
  max-width: 400px;
  height: 280px;
  background: linear-gradient(135deg, var(--secondary-50) 0%, var(--primary-50) 100%);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin: 0 auto var(--space-6) auto;
  border: 2px solid var(--secondary-200);
  box-shadow: var(--shadow-lg);
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: 1;
}

.image {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
  position: relative;
  z-index: 2;
}

.image:hover {
  transform: scale(1.02);
}

.imageLoading {
  opacity: 0;
}

/* Loading State */
.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--primary-200);
  border-top: 3px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loadingText {
  color: var(--primary-700);
  font-size: var(--text-sm);
  font-weight: 500;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Placeholder State */
.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  text-align: center;
  padding: var(--space-4);
  position: relative;
  z-index: 2;
}

.placeholderIcon {
  font-size: 3rem;
  opacity: 0.6;
  filter: grayscale(1);
}

.placeholderText {
  color: var(--secondary-500);
  font-size: var(--text-sm);
  font-weight: 500;
  margin: 0;
  line-height: 1.4;
}

/* Error State */
.errorState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  text-align: center;
  padding: var(--space-4);
  position: relative;
  z-index: 2;
}

.errorIcon {
  font-size: 2.5rem;
  opacity: 0.6;
  filter: grayscale(1);
}

.errorText {
  color: var(--secondary-500);
  font-size: var(--text-sm);
  font-weight: 500;
  margin: 0;
}

.errorActions {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.retryButton, .placeholderButton {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--primary-300);
  background: var(--primary-50);
  color: var(--primary-700);
  border-radius: var(--radius-lg);
  font-size: var(--text-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.retryButton:hover, .placeholderButton:hover {
  background: var(--primary-100);
  border-color: var(--primary-400);
  transform: translateY(-1px);
}

.retryButton:active, .placeholderButton:active {
  transform: translateY(0);
}

/* Fallback Info Badge */
.fallbackInfo {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  z-index: 4;
}

.fallbackBadge {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 500;
  backdrop-filter: blur(5px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    height: 220px;
    max-width: 100%;
  }
  
  .placeholderIcon, .errorIcon {
    font-size: 2rem;
  }
  
  .loadingSpinner {
    width: 32px;
    height: 32px;
    border-width: 2px;
  }
}

@media (max-width: 480px) {
  .container {
    height: 180px;
  }
  
  .errorActions {
    flex-direction: column;
    width: 100%;
  }
  
  .retryButton, .placeholderButton {
    width: 100%;
  }
}
