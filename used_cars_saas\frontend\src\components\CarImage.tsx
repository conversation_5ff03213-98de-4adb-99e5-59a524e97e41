import React, { useState, useEffect } from 'react';
import { CarImageDetails, getCarImageUrls, getCarPlaceholderImage } from '../services/apiService';
import styles from './CarImage.module.css';

interface CarImageProps {
  carDetails: CarImageDetails;
  className?: string;
  alt?: string;
  onImageLoad?: () => void;
  onImageError?: () => void;
  showFallbackInfo?: boolean;
}

const CarImage: React.FC<CarImageProps> = ({
  carDetails,
  className,
  alt,
  onImageLoad,
  onImageError,
  showFallbackInfo = false
}) => {
  const [currentImageUrl, setCurrentImageUrl] = useState<string>('');
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [currentUrlIndex, setCurrentUrlIndex] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<boolean>(false);
  const [fallbackUsed, setFallbackUsed] = useState<boolean>(false);

  // Initialize image URLs when car details change
  useEffect(() => {
    if (carDetails.make && carDetails.model && carDetails.year) {
      const urls = getCarImageUrls(carDetails);
      setImageUrls(urls);
      setCurrentUrlIndex(0);
      setCurrentImageUrl(urls[0]);
      setIsLoading(true);
      setHasError(false);
      setFallbackUsed(false);
    } else {
      setCurrentImageUrl('');
      setIsLoading(false);
    }
  }, [carDetails.make, carDetails.model, carDetails.year]);

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
    onImageLoad?.();
  };

  const handleImageError = () => {
    console.warn(`Failed to load image: ${currentImageUrl}`);

    // Try next URL in the fallback chain
    const nextIndex = currentUrlIndex + 1;
    if (nextIndex < imageUrls.length) {
      console.log(`Trying fallback image ${nextIndex + 1}/${imageUrls.length}`);
      setCurrentUrlIndex(nextIndex);
      setCurrentImageUrl(imageUrls[nextIndex]);
      setFallbackUsed(nextIndex > 0);
    } else {
      // All URLs failed, show error state
      console.error('All image URLs failed, showing error state');
      setIsLoading(false);
      setHasError(true);
      setFallbackUsed(true);
      onImageError?.();
    }
  };

  const retryImage = () => {
    if (imageUrls.length > 0) {
      setCurrentUrlIndex(0);
      setCurrentImageUrl(imageUrls[0]);
      setIsLoading(true);
      setHasError(false);
      setFallbackUsed(false);
    }
  };

  const useDirectPlaceholder = () => {
    const placeholderUrl = getCarPlaceholderImage(carDetails);
    setCurrentImageUrl(placeholderUrl);
    setIsLoading(true);
    setHasError(false);
    setFallbackUsed(true);
  };

  if (!carDetails.make || !carDetails.model || !carDetails.year) {
    return (
      <div className={`${styles.container} ${className || ''}`}>
        <div className={styles.placeholder}>
          <div className={styles.placeholderIcon}>🚗</div>
          <p className={styles.placeholderText}>Select car details to view image</p>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className={`${styles.container} ${className || ''}`}>
        <div className={styles.errorState}>
          <div className={styles.errorIcon}>📷</div>
          <p className={styles.errorText}>Image not available</p>
          <div className={styles.errorActions}>
            <button onClick={retryImage} className={styles.retryButton}>
              Retry
            </button>
            <button onClick={useDirectPlaceholder} className={styles.placeholderButton}>
              Use Placeholder
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${styles.container} ${className || ''}`}>
      {isLoading && (
        <div className={styles.loadingState}>
          <div className={styles.loadingSpinner}></div>
          <p className={styles.loadingText}>Loading image...</p>
        </div>
      )}

      {currentImageUrl && (
        <img
          src={currentImageUrl}
          alt={alt || `${carDetails.year} ${carDetails.make} ${carDetails.model}`}
          className={`${styles.image} ${isLoading ? styles.imageLoading : ''}`}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      )}

      {showFallbackInfo && fallbackUsed && !isLoading && !hasError && (
        <div className={styles.fallbackInfo}>
          <span className={styles.fallbackBadge}>
            {currentUrlIndex === imageUrls.length - 1 ? 'Placeholder' : 'Fallback'} Image
          </span>
        </div>
      )}
    </div>
  );
};

export default CarImage;
