.card {
  background: rgba(30, 30, 35, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

/* Variants */
.default {
  box-shadow: var(--shadow-md);
}

.elevated {
  box-shadow: var(--shadow-xl);
  background: rgba(30, 30, 35, 0.95);
}

.outlined {
  border: 2px solid var(--secondary-200);
  box-shadow: var(--shadow-sm);
}

.glass {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-lg);
}

/* Sizes */
.small {
  padding: var(--space-4);
}

.medium {
  padding: var(--space-6);
}

.large {
  padding: var(--space-8);
}

/* Interactive states */
.hoverable:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.clickable {
  cursor: pointer;
}

.clickable:active {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Animated entrance */
.animated {
  animation: fadeIn var(--animation-duration-normal) ease-out;
}

/* Decorative elements */
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-rainbow);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.hoverable:hover::before {
  opacity: 1;
}

/* Glass morphism effect for glass variant */
.glass::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .large {
    padding: var(--space-6);
  }
  
  .medium {
    padding: var(--space-4);
  }
  
  .small {
    padding: var(--space-3);
  }
}
