import React, { useState, useEffect, useCallback } from 'react';
import Plot from 'react-plotly.js';
import { getFeatureImportance } from '../services/apiService';
import { FeatureImportanceDataPoint } from '../interfaces';
import styles from './PredictorForm.module.css';

const FeatureImportanceChart: React.FC = () => {
    const [chartData, setChartData] = useState<FeatureImportanceDataPoint[]>([]);
    const [modelType, setModelType] = useState<'main' | 'error'>('main');
    const [topN, setTopN] = useState<number>(15);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const fetchChartData = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        try {
            const response = await getFeatureImportance(modelType, topN);
            // Sort by importance for consistent animation, Plotly might reorder based on y-axis values
            setChartData(response.data.sort((a, b) => a.importance - b.importance)); 
        } catch (err) {
            console.error("Failed to fetch feature importance data", err);
            setError("Could not load feature importance data.");
            setChartData([]);
        }
        setIsLoading(false);
    }, [modelType, topN]);

    useEffect(() => {
        fetchChartData();
    }, [fetchChartData]);

    // Prepare data for Plotly horizontal bar chart
    const plotlyData: Plotly.Data[] = [
        {
            y: chartData.map(d => d.feature_name),
            x: chartData.map(d => d.importance),
            type: 'bar',
            orientation: 'h',
            marker: { color: '#28a745' }, // Green color
        },
    ];

    const layout: Partial<Plotly.Layout> = {
        title: { text: `Top ${topN} Features - ${modelType === 'main' ? 'Main Model' : 'Error Model'}` },
        xaxis: { title: { text: 'Importance Score' } },
        yaxis: { title: { text: 'Feature' }, automargin: true },
        margin: { l: 150, r: 20, b: 50, t: 50 },
    };
    
    return (
        <div>
            <h4>Feature Importance Controls:</h4>
            <div className={styles.filterContainer}>
                <div className={styles.filterGroup}>
                    <label htmlFor="modelTypeSelect">Model Type: </label>
                    <select id="modelTypeSelect" value={modelType} onChange={e => setModelType(e.target.value as 'main' | 'error')} className={styles.formSelect}>
                        <option value="main">Main Model</option>
                        <option value="error">Error Model</option>
                    </select>
                </div>
                <div className={styles.filterGroup}>
                    <label htmlFor="topNInput">Top N Features: </label>
                    <input 
                        type="number" 
                        id="topNInput" 
                        value={topN} 
                        onChange={e => setTopN(Number(e.target.value))} 
                        min="5" 
                        max="50" 
                        step="1"
                        className={styles.formInput}
                    />
                </div>
            </div>

            {isLoading && <p className={styles.loadingMessage}>Loading chart data...</p>}
            {error && <p className={styles.errorMessage}>{error}</p>}
            {!isLoading && !error && chartData.length === 0 && <p className={styles.noDataMessage}>No feature importance data available.</p>}
            {!isLoading && !error && chartData.length > 0 && (
                <Plot 
                    data={plotlyData} 
                    layout={layout} 
                    style={{ width: '100%', height: `${Math.max(400, chartData.length * 25)}px` }}
                />
            )}
        </div>
    );
};

export default FeatureImportanceChart; 