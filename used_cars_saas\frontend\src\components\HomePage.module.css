.page {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8);
    display: flex;
    flex-direction: column;
    gap: var(--space-12);
}

.hero {
    text-align: center;
    padding: var(--space-20) var(--space-8);
    background: linear-gradient(135deg,
        rgba(30, 30, 35, 0.95) 0%,
        rgba(30, 30, 35, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-3xl);
    box-shadow: var(--shadow-2xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(168, 85, 247, 0.05) 50%,
        transparent 100%);
    z-index: 0;
}

.hero h2 {
    font-size: var(--text-6xl);
    font-weight: 900;
    margin-bottom: var(--space-6);
    line-height: 1.1;
    position: relative;
    z-index: 1;
    background: var(--gradient-rainbow);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

.hero p {
    font-size: var(--text-xl);
    color: var(--gray-200);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
    position: relative;
    z-index: 1;
    font-weight: 500;
}

.section {
    padding: var(--space-12);
    background: linear-gradient(135deg,
        rgba(30, 30, 35, 0.95) 0%,
        rgba(30, 30, 35, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-3xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-spring);
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-rainbow);
    z-index: 1;
}

.section:hover {
    box-shadow: var(--shadow-2xl);
    transform: translateY(-8px);
    border-color: rgba(255, 255, 255, 0.2);
}

.section h3 {
    font-size: var(--text-3xl);
    font-weight: 800;
    margin-bottom: var(--space-8);
    text-align: center;
    position: relative;
    z-index: 2;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section h4 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-200);
    margin: var(--space-8) 0 var(--space-6) 0;
    position: relative;
    padding-left: var(--space-6);
}

.section h4::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: var(--gradient-secondary);
    border-radius: var(--radius);
}

.section p {
    color: var(--gray-300);
    line-height: 1.8;
    margin-bottom: var(--space-6);
    font-size: var(--text-lg);
    position: relative;
    z-index: 2;
}

.link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-5) var(--space-10);
    background: var(--gradient-primary);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-2xl);
    font-weight: 700;
    font-size: var(--text-lg);
    transition: var(--transition-spring);
    box-shadow: var(--shadow-primary);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    z-index: 2;
}

.link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    transition: var(--transition-spring);
    z-index: -1;
}

.link:hover::before {
    left: 0;
}

.link:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: var(--shadow-2xl);
    color: var(--white);
    border-color: var(--secondary-300);
}

.link:active {
    transform: translateY(-2px) scale(1.02);
}

.errorMessage {
    padding: var(--space-6);
    background: linear-gradient(135deg, var(--error-50) 0%, rgba(254, 242, 242, 0.8) 100%);
    color: var(--error-700);
    border: 2px solid var(--error-200);
    border-radius: var(--radius-2xl);
    margin: var(--space-6) 0;
    font-weight: 600;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        padding: var(--space-12) var(--space-4);
    }

    .hero h2 {
        font-size: var(--text-3xl);
    }

    .section {
        padding: var(--space-6);
    }

    .section h3 {
        font-size: var(--text-xl);
    }
}

