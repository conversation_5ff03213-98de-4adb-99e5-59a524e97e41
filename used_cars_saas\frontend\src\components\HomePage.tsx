import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styles from './HomePage.module.css'; // Import CSS Module
import PriceDistributionChart from './PriceDistributionChart';
import Scatter3DChart from './Scatter3DChart';
import FeatureImportanceChart from './FeatureImportanceChart';
import LoadingSpinner from './LoadingSpinner';
import { getDropdownOptions, getModelStatus, loadMainModel, ModelStatus } from '../services/apiService';
import { DropdownOptions } from '../interfaces';

const HomePage: React.FC = () => {
    const [dropdownOptions, setDropdownOptions] = useState<DropdownOptions | null>(null);
    const [isLoadingOptions, setIsLoadingOptions] = useState<boolean>(true);
    const [optionsError, setOptionsError] = useState<string | null>(null);
    const [modelStatus, setModelStatus] = useState<ModelStatus | null>(null);
    const [isLoadingMainModel, setIsLoadingMainModel] = useState<boolean>(false);

    useEffect(() => {
        const fetchOptions = async () => {
            setIsLoadingOptions(true);
            try {
                const options = await getDropdownOptions();
                setDropdownOptions(options);
                setOptionsError(null);
            } catch (error) {
                console.error("Failed to load dropdown options for HomePage", error);
                setOptionsError("Could not load filter options for some charts. They may not work correctly if they rely on these options.");
                setDropdownOptions(null);
            }
            setIsLoadingOptions(false);
        };

        const fetchModelStatus = async () => {
            try {
                const status = await getModelStatus();
                setModelStatus(status);
            } catch (error) {
                console.error("Failed to load model status", error);
            }
        };

        fetchOptions();
        fetchModelStatus();
    }, []);

    const handleLoadMainModel = async () => {
        setIsLoadingMainModel(true);
        try {
            await loadMainModel();
            // Refresh model status after loading
            const status = await getModelStatus();
            setModelStatus(status);
        } catch (error) {
            console.error("Failed to load main model", error);
        }
        setIsLoadingMainModel(false);
    };

    return (
        <div className={`${styles.page} animate-fade-in`}>
            <div className={`${styles.hero} animate-scale-in hover-lift`}>
                <h2 className="animate-slide-in-top">Welcome to the US Used Cars Platform</h2>
                <p className="animate-slide-in-bottom">
                    Leveraging advanced machine learning, this platform offers precise price predictions for used vehicles across the United States.
                    Dive into interactive data visualizations to understand market dynamics or get a specific valuation using our predictor tool.
                </p>
            </div>

            {/* Model Status Section */}
            {modelStatus && (
                <section className={`${styles.section} animate-slide-in-left hover-lift`}>
                    <h3>🤖 AI Model Status</h3>
                    <div style={{
                        padding: '1rem',
                        backgroundColor: modelStatus.can_predict ? '#d4edda' : '#fff3cd',
                        border: `1px solid ${modelStatus.can_predict ? '#c3e6cb' : '#ffeaa7'}`,
                        borderRadius: '8px',
                        marginBottom: '1rem'
                    }}>
                        <p><strong>Main Model:</strong> {modelStatus.main_model_loaded ? '✅ Loaded' : '⏳ Not Loaded'}</p>
                        <p><strong>Error Model:</strong> {modelStatus.error_model_loaded ? '✅ Loaded' : '❌ Not Loaded'}</p>
                        <p><strong>Prediction Service:</strong> {modelStatus.can_predict ? '🟢 Ready' : '🟡 Limited'}</p>

                        {!modelStatus.main_model_loaded && (
                            <div style={{ marginTop: '1rem' }}>
                                <p style={{ fontSize: '0.9rem', color: '#666' }}>
                                    The main model (14.8 GB) may have failed to load automatically or timed out during startup.
                                    You can try loading it manually to enable full prediction capabilities.
                                </p>
                                <button
                                    onClick={handleLoadMainModel}
                                    disabled={isLoadingMainModel}
                                    style={{
                                        padding: '0.5rem 1rem',
                                        backgroundColor: '#007bff',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '4px',
                                        cursor: isLoadingMainModel ? 'not-allowed' : 'pointer',
                                        opacity: isLoadingMainModel ? 0.6 : 1
                                    }}
                                >
                                    {isLoadingMainModel ? '⏳ Loading Main Model...' : '🔄 Retry Loading Main Model'}
                                </button>
                            </div>
                        )}
                    </div>
                </section>
            )}

            <section className={styles.section}>
                <h3>Overview</h3>
                <p>
                    The US used car market is vast and often complex for buyers and sellers to navigate.
                    This platform aims to demystify vehicle valuation by employing a sophisticated two-stage CatBoost
                    machine learning model, trained on nearly 3 million diverse listings from 2006 to 2020.
                    Our model analyzes approximately 40 distinct features – encompassing everything from basic characteristics
                    like make, model, year, and mileage, to more nuanced attributes such as engine specifications,
                    trim details, and market conditions (derived from listing dates).
                </p>
                <p>
                    The core of our prediction engine involves a primary CatBoost model that estimates the car's price,
                    followed by a second CatBoost model that specifically learns to correct the errors of the first.
                    This ensemble approach has demonstrated high accuracy (R² of ~0.99), providing reliable estimates.
                    Beyond just predictions, the platform allows users to explore aggregated data insights through dynamic
                    visualizations on this Home Page, offering a clearer understanding of the factors that drive used car prices.
                    Our goal is to empower users with transparent, data-driven information, whether they are casually browsing,
                    looking to buy, or planning to sell a vehicle.
                </p>
            </section>

            <section className={`${styles.section} animate-slide-in-right hover-lift`}>
                <h3>📊 Interactive Data Visualizations</h3>
                {isLoadingOptions && (
                    <LoadingSpinner
                        variant="ring"
                        color="primary"
                        message="Loading chart options..."
                    />
                )}
                {optionsError && <p className={styles.errorMessage}>{optionsError}</p>}

                <h4>Price Distribution</h4>
                {dropdownOptions || !isLoadingOptions ? (
                    <PriceDistributionChart dropdownOptions={dropdownOptions} />
                ) : (
                    <LoadingSpinner
                        variant="bars"
                        color="secondary"
                        message="Loading price distribution chart..."
                    />
                )}

                <br />
                <h4>3D Scatter Plot: Price vs. Mileage vs. Car Age</h4>
                {dropdownOptions || !isLoadingOptions ? (
                    <Scatter3DChart dropdownOptions={dropdownOptions} />
                ) : (
                    <LoadingSpinner
                        variant="pulse"
                        color="accent"
                        message="Loading 3D scatter plot..."
                    />
                )}

                <br />
                <h4>Feature Importance</h4>
                <FeatureImportanceChart />
            </section>

            <section className={`${styles.section} animate-scale-in hover-lift`}>
                <h3>🚗 Get a Price Prediction</h3>
                <p>
                    Ready to discover your car's value? Navigate to our advanced price predictor to get an instant,
                    AI-powered valuation based on comprehensive market analysis.
                </p>
                <div style={{ textAlign: 'center', marginTop: 'var(--space-8)' }}>
                    <Link to="/predictor" className={`${styles.link} hover-glow animate-float`}>
                        🎯 Start Price Prediction
                    </Link>
                </div>
            </section>
        </div>
    );
};

export default HomePage;