.container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-6);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.container h3 {
  color: var(--secondary-900);
  font-size: var(--text-2xl);
  font-weight: 600;
  margin-bottom: var(--space-4);
  text-align: center;
  font-family: var(--font-family-heading);
}

.description {
  color: var(--secondary-600);
  font-size: var(--text-base);
  line-height: 1.6;
  margin-bottom: var(--space-6);
  text-align: center;
}

.controls {
  margin-bottom: var(--space-6);
  text-align: center;
}

.controls label {
  display: block;
  margin-bottom: var(--space-2);
  color: var(--secondary-700);
  font-weight: 500;
  font-size: var(--text-sm);
}

.select {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--secondary-200);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  color: var(--secondary-800);
  background: white;
  min-width: 300px;
  transition: all var(--transition-fast);
}

.select:focus {
  border-color: var(--primary-500);
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.imageContainer {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-8);
}

.demoImage {
  max-width: 400px;
  width: 100%;
}

.info {
  background: var(--secondary-50);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--secondary-200);
}

.info h4 {
  color: var(--secondary-900);
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  font-family: var(--font-family-heading);
}

.fallbackSteps {
  margin: var(--space-4) 0;
  padding-left: var(--space-6);
}

.fallbackSteps li {
  margin-bottom: var(--space-2);
  color: var(--secondary-700);
  line-height: 1.5;
}

.fallbackSteps strong {
  color: var(--primary-600);
}

.note {
  background: var(--warning-50);
  border: 1px solid var(--warning-200);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-4);
  font-size: var(--text-sm);
  color: var(--warning-800);
}

.note strong {
  color: var(--warning-900);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: var(--space-4);
    margin: var(--space-4);
  }
  
  .select {
    min-width: 250px;
    font-size: var(--text-sm);
  }
  
  .demoImage {
    max-width: 100%;
  }
  
  .info {
    padding: var(--space-4);
  }
  
  .fallbackSteps {
    padding-left: var(--space-4);
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--space-3);
    margin: var(--space-2);
  }
  
  .select {
    min-width: 200px;
    width: 100%;
  }
  
  .container h3 {
    font-size: var(--text-xl);
  }
}
