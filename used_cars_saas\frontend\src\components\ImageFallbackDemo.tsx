import React, { useState } from 'react';
import CarImage from './CarImage';
import { CarImageDetails } from '../interfaces';
import styles from './ImageFallbackDemo.module.css';

const ImageFallbackDemo: React.FC = () => {
  const [selectedCar, setSelectedCar] = useState<CarImageDetails>({
    make: 'Toyota',
    model: 'Camry',
    year: 2020
  });

  const demoCarOptions = [
    { make: 'Toyota', model: 'Camry', year: 2020, bodyType: 'sedan' },
    { make: 'BMW', model: 'X5', year: 2021, bodyType: 'suv' },
    { make: 'Ford', model: 'F-150', year: 2022, bodyType: 'truck' },
    { make: 'Porsche', model: '911', year: 2023, bodyType: 'sports' },
    { make: 'NonExistent', model: 'TestCar', year: 2024, bodyType: 'sedan' }, // This will definitely fail and show fallbacks
  ];

  return (
    <div className={styles.container}>
      <h3>Car Image Fallback System Demo</h3>
      <p className={styles.description}>
        This demo shows how our image system ensures every car has a visual representation.
        Try different cars to see the fallback system in action.
      </p>
      
      <div className={styles.controls}>
        <label htmlFor="car-select">Select a car to test:</label>
        <select 
          id="car-select"
          value={`${selectedCar.make}-${selectedCar.model}-${selectedCar.year}`}
          onChange={(e) => {
            const [make, model, year] = e.target.value.split('-');
            const carOption = demoCarOptions.find(car => 
              car.make === make && car.model === model && car.year === parseInt(year)
            );
            if (carOption) {
              setSelectedCar(carOption);
            }
          }}
          className={styles.select}
        >
          {demoCarOptions.map((car, index) => (
            <option key={index} value={`${car.make}-${car.model}-${car.year}`}>
              {car.year} {car.make} {car.model}
              {car.make === 'NonExistent' ? ' (Test Fallback)' : ''}
            </option>
          ))}
        </select>
      </div>

      <div className={styles.imageContainer}>
        <CarImage 
          carDetails={selectedCar}
          showFallbackInfo={true}
          className={styles.demoImage}
        />
      </div>

      <div className={styles.info}>
        <h4>How the fallback system works:</h4>
        <ol className={styles.fallbackSteps}>
          <li><strong>Primary:</strong> Imagin Studio API (professional car images)</li>
          <li><strong>Secondary:</strong> Unsplash stock photos (real car photos by type)</li>
          <li><strong>Final:</strong> Custom SVG illustrations (always available)</li>
        </ol>
        <p className={styles.note}>
          <strong>Note:</strong> The "NonExistent TestCar" option will demonstrate the complete fallback chain
          since no real images exist for this fictional car.
        </p>
      </div>
    </div>
  );
};

export default ImageFallbackDemo;
