.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
}

/* Dots Spinner (original) */
.dotsSpinner {
  display: flex;
  gap: var(--space-1);
}

.bounce1, .bounce2, .bounce3 {
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.bounce1 {
  animation-delay: -0.32s;
}

.bounce2 {
  animation-delay: -0.16s;
}

.bounce3 {
  animation-delay: 0s;
}

/* Ring Spinner */
.ringSpinner {
  display: inline-block;
  position: relative;
}

.ringSpinner div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  border-radius: 50%;
  animation: ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-style: solid;
}

.ringSpinner div:nth-child(1) { animation-delay: -0.45s; }
.ringSpinner div:nth-child(2) { animation-delay: -0.3s; }
.ringSpinner div:nth-child(3) { animation-delay: -0.15s; }

/* Pulse Spinner */
.pulseSpinner {
  display: inline-block;
  position: relative;
}

.pulseSpinner div {
  border-radius: 50%;
  animation: pulse-scale 1s ease-in-out infinite;
}

/* Bars Spinner */
.barsSpinner {
  display: inline-block;
  position: relative;
}

.barsSpinner div {
  display: inline-block;
  position: absolute;
  left: 8px;
  animation: bars 1.2s cubic-bezier(0, 0.5, 0.5, 1) infinite;
}

.barsSpinner div:nth-child(1) { left: 8px; animation-delay: -0.24s; }
.barsSpinner div:nth-child(2) { left: 32px; animation-delay: -0.12s; }
.barsSpinner div:nth-child(3) { left: 56px; animation-delay: 0; }
.barsSpinner div:nth-child(4) { left: 80px; animation-delay: 0.12s; }
.barsSpinner div:nth-child(5) { left: 104px; animation-delay: 0.24s; }

/* Size variants for dots */
.small .bounce1, .small .bounce2, .small .bounce3 {
  width: 8px;
  height: 8px;
}

.medium .bounce1, .medium .bounce2, .medium .bounce3 {
  width: 12px;
  height: 12px;
}

.large .bounce1, .large .bounce2, .large .bounce3 {
  width: 16px;
  height: 16px;
}

/* Size variants for ring */
.small.ringSpinner {
  width: 24px;
  height: 24px;
}

.small.ringSpinner div {
  width: 20px;
  height: 20px;
  margin: 2px;
  border-width: 2px;
}

.medium.ringSpinner {
  width: 32px;
  height: 32px;
}

.medium.ringSpinner div {
  width: 28px;
  height: 28px;
  margin: 2px;
  border-width: 3px;
}

.large.ringSpinner {
  width: 48px;
  height: 48px;
}

.large.ringSpinner div {
  width: 44px;
  height: 44px;
  margin: 2px;
  border-width: 4px;
}

/* Size variants for pulse */
.small.pulseSpinner div {
  width: 24px;
  height: 24px;
}

.medium.pulseSpinner div {
  width: 32px;
  height: 32px;
}

.large.pulseSpinner div {
  width: 48px;
  height: 48px;
}

/* Size variants for bars */
.small.barsSpinner {
  width: 60px;
  height: 20px;
}

.small.barsSpinner div {
  width: 4px;
  height: 20px;
}

.medium.barsSpinner {
  width: 80px;
  height: 32px;
}

.medium.barsSpinner div {
  width: 6px;
  height: 32px;
}

.large.barsSpinner {
  width: 120px;
  height: 48px;
}

.large.barsSpinner div {
  width: 8px;
  height: 48px;
}

.message {
  margin-top: var(--space-3);
  color: var(--secondary-600);
  font-size: var(--text-sm);
  font-weight: 500;
  text-align: center;
}

.small .message {
  font-size: var(--text-xs);
  margin-top: var(--space-2);
}

.large .message {
  font-size: var(--text-base);
  margin-top: var(--space-4);
}

/* Color variants */
.primary .bounce1, .primary .bounce2, .primary .bounce3 {
  background: var(--gradient-primary);
}

.primary.ringSpinner div {
  border-color: var(--primary-500) transparent transparent transparent;
}

.primary.pulseSpinner div {
  background: var(--gradient-primary);
}

.primary.barsSpinner div {
  background: var(--primary-500);
}

.secondary .bounce1, .secondary .bounce2, .secondary .bounce3 {
  background: var(--gradient-secondary);
}

.secondary.ringSpinner div {
  border-color: var(--secondary-500) transparent transparent transparent;
}

.secondary.pulseSpinner div {
  background: var(--gradient-secondary);
}

.secondary.barsSpinner div {
  background: var(--secondary-500);
}

.accent .bounce1, .accent .bounce2, .accent .bounce3 {
  background: var(--gradient-accent);
}

.accent.ringSpinner div {
  border-color: var(--accent-500) transparent transparent transparent;
}

.accent.pulseSpinner div {
  background: var(--gradient-accent);
}

.accent.barsSpinner div {
  background: var(--accent-500);
}

.success .bounce1, .success .bounce2, .success .bounce3 {
  background: var(--gradient-success);
}

.success.ringSpinner div {
  border-color: var(--success-500) transparent transparent transparent;
}

.success.pulseSpinner div {
  background: var(--gradient-success);
}

.success.barsSpinner div {
  background: var(--success-500);
}

.warning .bounce1, .warning .bounce2, .warning .bounce3 {
  background: var(--gradient-warning);
}

.warning.ringSpinner div {
  border-color: var(--warning-500) transparent transparent transparent;
}

.warning.pulseSpinner div {
  background: var(--gradient-warning);
}

.warning.barsSpinner div {
  background: var(--warning-500);
}

.error .bounce1, .error .bounce2, .error .bounce3 {
  background: var(--gradient-error);
}

.error.ringSpinner div {
  border-color: var(--error-500) transparent transparent transparent;
}

.error.pulseSpinner div {
  background: var(--gradient-error);
}

.error.barsSpinner div {
  background: var(--error-500);
}

/* Animations */
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse-scale {
  0%, 100% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1);
    opacity: 0.5;
  }
}

@keyframes bars {
  0% {
    top: 8px;
    height: 64px;
  }
  50%, 100% {
    top: 24px;
    height: 32px;
  }
}

/* Alternative pulse animation */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Shimmer effect for loading states */
.shimmer {
  background: linear-gradient(90deg, 
    var(--secondary-200) 25%, 
    var(--secondary-100) 50%, 
    var(--secondary-200) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
