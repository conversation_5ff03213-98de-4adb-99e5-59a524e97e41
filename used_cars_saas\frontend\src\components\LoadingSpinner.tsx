import React from 'react';
import styles from './LoadingSpinner.module.css';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  variant?: 'dots' | 'ring' | 'pulse' | 'bars';
  color?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error';
  message?: string;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  variant = 'dots',
  color = 'primary',
  message = 'Loading...',
  className = ''
}) => {
  const renderSpinner = () => {
    switch (variant) {
      case 'ring':
        return (
          <div className={`${styles.ringSpinner} ${styles[size]} ${styles[color]}`}>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
        );
      case 'pulse':
        return (
          <div className={`${styles.pulseSpinner} ${styles[size]} ${styles[color]}`}>
            <div></div>
          </div>
        );
      case 'bars':
        return (
          <div className={`${styles.barsSpinner} ${styles[size]} ${styles[color]}`}>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
        );
      default: // dots
        return (
          <div className={`${styles.dotsSpinner} ${styles[size]} ${styles[color]}`}>
            <div className={styles.bounce1}></div>
            <div className={styles.bounce2}></div>
            <div className={styles.bounce3}></div>
          </div>
        );
    }
  };

  return (
    <div className={`${styles.container} ${className}`}>
      {renderSpinner()}
      {message && <p className={`${styles.message} ${styles[size]}`}>{message}</p>}
    </div>
  );
};

export default LoadingSpinner;
