.explanationContainer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 24px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
}

.explanationContainer h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    text-align: center;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.confidenceSection {
    background: #e8f4fd;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    border-left: 4px solid #3498db;
}

.confidenceText {
    margin: 0;
    color: #2c3e50;
    font-size: 1rem;
}

.contributionsSection {
    margin-bottom: 20px;
}

.sectionDescription {
    color: #5a6c7d;
    font-size: 0.95rem;
    margin-bottom: 16px;
    text-align: center;
    font-style: italic;
}

.contributionsList {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contributionItem {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.contributionItem:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.contributionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.featureName {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
}

.featureValue {
    color: #6c757d;
    font-size: 0.9rem;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.contributionBarContainer {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    margin: 8px 0;
    overflow: hidden;
}

.contributionBar {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.contributionBar.positive {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.contributionBar.negative {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.impactDescription {
    text-align: center;
    margin-top: 8px;
}

.impactText {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
}

.impactText.positiveText {
    color: #27ae60;
    background: #d5f4e6;
}

.impactText.negativeText {
    color: #e74c3c;
    background: #fdeaea;
}

.summarySection {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    border: 1px solid #dee2e6;
}

.summaryText {
    margin: 8px 0;
    color: #495057;
    font-size: 0.95rem;
}

.summaryNote {
    color: #6c757d;
    font-size: 0.85rem;
    font-style: italic;
}

.finalPrice {
    margin: 12px 0 8px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    text-align: center;
    padding: 12px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.3);
}

.explanationNote {
    margin-top: 16px;
    padding: 12px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    color: #856404;
    font-size: 0.9rem;
    text-align: center;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    .explanationContainer {
        padding: 16px;
        margin: 16px 0;
    }
    
    .contributionHeader {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .featureValue {
        align-self: flex-end;
    }
}
