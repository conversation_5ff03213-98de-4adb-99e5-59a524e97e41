import React from 'react';
import { PredictionExplanationResponse } from '../interfaces';
import styles from './PredictionExplanation.module.css';

interface PredictionExplanationProps {
    explanation: PredictionExplanationResponse;
}

const PredictionExplanation: React.FC<PredictionExplanationProps> = ({ explanation }) => {
    if (!explanation || !explanation.shap_contributions) return null;

    const maxAbsShap = Math.max(...explanation.shap_contributions.map(item => Math.abs(item.shap_value)), 0.1);

    return (
        <div className={styles.explanationContainer}>
            <h4>Why This Price? (Personalized Analysis)</h4>

            {/* Confidence Interval */}
            {explanation.confidence_interval && (
                <div className={styles.confidenceSection}>
                    <p className={styles.confidenceText}>
                        <strong>Price Range:</strong> ${explanation.confidence_interval.lower.toLocaleString()} - ${explanation.confidence_interval.upper.toLocaleString()}
                    </p>
                </div>
            )}

            {/* SHAP Contributions */}
            <div className={styles.contributionsSection}>
                <p className={styles.sectionDescription}>
                    How each feature of <strong>this specific car</strong> affects its price:
                </p>

                <ul className={styles.contributionsList}>
                    {explanation.shap_contributions.slice(0, 7).map((item, index) => (
                        <li key={index} className={styles.contributionItem}>
                            <div className={styles.contributionHeader}>
                                <span className={styles.featureName}>{item.feature_name}</span>
                                <span className={styles.featureValue}>{item.feature_value}</span>
                            </div>

                            <div className={styles.contributionBarContainer}>
                                <div
                                    className={`${styles.contributionBar} ${item.shap_value >= 0 ? styles.positive : styles.negative}`}
                                    style={{
                                        width: `${(Math.abs(item.shap_value) / maxAbsShap) * 100}%`
                                    }}
                                />
                            </div>

                            <div className={styles.impactDescription}>
                                <span className={`${styles.impactText} ${item.shap_value >= 0 ? styles.positiveText : styles.negativeText}`}>
                                    {item.impact_description}
                                </span>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>

            {/* Summary */}
            <div className={styles.summarySection}>
                <p className={styles.summaryText}>
                    <strong>Base Price:</strong> ${explanation.base_price.toLocaleString()}
                    <span className={styles.summaryNote}> (average market price)</span>
                </p>
                <p className={styles.summaryText}>
                    <strong>Your Car's Adjustments:</strong> {explanation.total_shap_sum >= 0 ? '+' : ''}${(explanation.predicted_price - explanation.base_price).toLocaleString()}
                </p>
                <p className={styles.finalPrice}>
                    <strong>Final Predicted Price: ${explanation.predicted_price.toLocaleString()}</strong>
                </p>
            </div>

            <p className={styles.explanationNote}>
                💡 This analysis is specific to your car's exact features and shows how each one contributes to the final price prediction.
            </p>
        </div>
    );
};

export default PredictionExplanation;
