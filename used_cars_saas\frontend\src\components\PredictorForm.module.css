.formContainer {
    max-width: 700px;
    margin: 0 auto;
    padding: var(--space-12);
    background: linear-gradient(135deg,
        rgba(30, 30, 35, 0.95) 0%,
        rgba(30, 30, 35, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-3xl);
    box-shadow: var(--shadow-2xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.formContainer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-rainbow);
    z-index: 1;
}

.formContainer h3 {
    text-align: center;
    margin-bottom: var(--space-10);
    font-size: var(--text-3xl);
    font-weight: 900;
    position: relative;
    z-index: 2;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

.formGroup {
    margin-bottom: var(--space-8);
    position: relative;
}

.formGroup label {
    display: block;
    margin-bottom: var(--space-3);
    color: var(--gray-200);
    font-weight: 700;
    font-size: var(--text-base);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.formGroup label::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--gradient-secondary);
    border-radius: var(--radius);
}

.formInput, .formSelect {
    width: 100%;
    padding: var(--space-5);
    border: 2px solid var(--gray-600);
    border-radius: var(--radius-2xl);
    font-size: var(--text-lg);
    color: var(--gray-100);
    background: rgba(30, 30, 35, 0.9);
    backdrop-filter: blur(10px);
    transition: var(--transition-spring);
    box-shadow: var(--shadow-lg);
    font-weight: 500;
}

.formInput:focus, .formSelect:focus {
    border-color: var(--primary-500);
    outline: none;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1), var(--shadow-xl);
    transform: translateY(-2px);
    background: rgba(40, 40, 45, 0.95);
}

.formInput:hover, .formSelect:hover {
    border-color: var(--primary-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-xl);
}

.formInput::placeholder {
    color: var(--secondary-400);
    font-style: italic;
}

.submitButton {
    width: 100%;
    padding: var(--space-6) var(--space-8);
    background: var(--gradient-rainbow);
    color: var(--white);
    border: none;
    border-radius: var(--radius-2xl);
    font-size: var(--text-xl);
    font-weight: 800;
    cursor: pointer;
    transition: var(--transition-spring);
    margin-top: var(--space-10);
    box-shadow: var(--shadow-2xl);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.submitButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-spring);
}

.submitButton:hover::before {
    left: 100%;
}

.submitButton:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
}

.submitButton:active {
    transform: translateY(-2px) scale(1.01);
}

.submitButton:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow);
}

.submitButton:disabled::before {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .formContainer {
        padding: var(--space-6);
    }
}

.loadingMessage, .errorMessage, .noDataMessage {
    text-align: center;
    margin-top: var(--space-4);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: 500;
}

.loadingMessage {
    color: var(--primary-700);
    background: var(--primary-50);
    border: 1px solid var(--primary-200);
}

.errorMessage {
    color: var(--error-700);
    background: var(--error-50);
    border: 1px solid var(--error-200);
}

.noDataMessage {
    color: var(--secondary-500);
    background: var(--secondary-50);
    border: 1px solid var(--secondary-200);
}

/* Enhanced filter controls for chart components */
.filterContainer {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    flex-wrap: wrap;
    justify-content: center;
    padding: var(--space-6);
    background: rgba(30, 30, 35, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-sm);
}

.filterGroup {
    display: flex;
    flex-direction: column;
    min-width: 200px;
    position: relative;
}

.filterGroup label {
    margin-bottom: var(--space-2);
    font-size: var(--text-sm);
    color: var(--secondary-300);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filterGroup select,
.filterGroup input {
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    border: 2px solid var(--secondary-700);
    font-size: var(--text-sm);
    color: var(--secondary-200);
    background: rgba(30, 30, 35, 0.9);
    transition: all var(--transition-fast);
    font-family: var(--font-family-primary);
}

.filterGroup select:focus,
.filterGroup input:focus {
    border-color: var(--primary-500);
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(40, 40, 45, 0.95);
}

.filterGroup select:hover,
.filterGroup input:hover {
    border-color: var(--primary-300);
    background: rgba(40, 40, 45, 0.95);
}

/* Car image container styling */
.carImageContainer {
    width: 100%;
    max-width: 400px;
    height: 280px;
    background: linear-gradient(135deg, var(--secondary-900) 0%, var(--primary-900) 100%);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin: 0 auto var(--space-6) auto;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
    position: relative;
}

.carImageContainer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

.carImage {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    border-radius: var(--radius-xl);
    transition: transform var(--transition-normal);
}

.carImage:hover {
    transform: scale(1.02);
}

/* Responsive Design */
@media (max-width: 768px) {
    .formContainer {
        margin: var(--space-4) auto;
        padding: var(--space-6);
        max-width: 90%;
    }

    .filterContainer {
        flex-direction: column;
        gap: var(--space-3);
        padding: var(--space-4);
    }

    .filterGroup {
        min-width: auto;
    }

    .carImageContainer {
        height: 220px;
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .formContainer {
        padding: var(--space-4);
        margin: var(--space-2) auto;
    }

    .formContainer h3 {
        font-size: var(--text-xl);
    }

    .submitButton {
        font-size: var(--text-base);
        padding: var(--space-3) var(--space-4);
    }

    .carImageContainer {
        height: 180px;
    }
}