import React, { useState, useEffect, FormEvent, useMemo, useRef } from 'react';
import { getDropdownOptions, submitPrediction } from '../services/apiService';
import { PredictionInput, PredictionOutput, CarImageDetails } from '../interfaces'; // Removed DropdownOptions as it's implicitly handled by props
import CarImage from './CarImage';
import LoadingSpinner from './LoadingSpinner';
import styles from './PredictorForm.module.css'; // Import CSS Module

interface PredictorFormProps {
    onPredictionSuccess: (data: PredictionOutput, inputData: PredictionInput) => void;
    onPredictionError: (message: string) => void;
}

const PredictorForm: React.FC<PredictorFormProps> = ({ onPredictionSuccess, onPredictionError }) => {
    const [make, setMake] = useState<string>('');
    const [model, setModel] = useState<string>('');
    const [cylinderCount, setCylinderCount] = useState<number | string>('');
    const [mileage, setMileage] = useState<number | string>('');
    const [year, setYear] = useState<number | string>('');

    const [availableMakes, setAvailableMakes] = useState<string[]>([]);
    const [availableModels, setAvailableModels] = useState<string[]>([]);
    const [availableCylinderCounts, setAvailableCylinderCounts] = useState<number[]>([]);
    const [makeModelMap, setMakeModelMap] = useState<{ [key: string]: string[] }>({});

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isFetchingOptions, setIsFetchingOptions] = useState<boolean>(true);

    useEffect(() => {
        const fetchOptions = async () => {
            setIsFetchingOptions(true);
            try {
                const options = await getDropdownOptions();
                setMakeModelMap(options.make_model_map);
                setAvailableMakes(Object.keys(options.make_model_map).sort());
                setAvailableCylinderCounts(options.cylinder_counts.sort((a, b) => a - b));
            } catch (error) {
                console.error("Failed to load dropdown options", error);
                onPredictionError("Failed to load form options. Please try refreshing.");
            }
            setIsFetchingOptions(false);
        };
        fetchOptions();
    }, [onPredictionError]);

    // Track the previous make to only reset model when make actually changes
    const previousMakeRef = useRef<string>('');

    useEffect(() => {
        if (make && makeModelMap[make]) {
            setAvailableModels(makeModelMap[make].sort());
            // Only reset model if the make has actually changed, not on re-renders
            if (make !== previousMakeRef.current) {
                setModel('');
                previousMakeRef.current = make;
            }
        } else {
            setAvailableModels([]);
            // Only reset model if we had a make before
            if (previousMakeRef.current !== '') {
                setModel('');
                previousMakeRef.current = '';
            }
        }
    }, [make, makeModelMap]);

    // Create car image details for the CarImage component (memoized to prevent unnecessary re-renders)
    const carImageDetails: CarImageDetails = useMemo(() => ({
        make: make,
        model: model,
        year: Number(year) || 0
    }), [make, model, year]);

    const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        onPredictionError('');

        if (!make || !model || cylinderCount === '' || mileage === '' || year === '') {
            onPredictionError("Please fill in all fields.");
            return;
        }
        const finalCylinderCount = cylinderCount === '' ? undefined : Number(cylinderCount);
        const finalMileage = mileage === '' ? undefined : Number(mileage);
        const finalYear = year === '' ? undefined : Number(year);

        if (finalCylinderCount === undefined || finalMileage === undefined || finalYear === undefined) {
            onPredictionError("Invalid numeric input for cylinders, mileage, or year.");
            return;
        }

        const predictionData: PredictionInput = {
            make_name: make,
            model_name: model,
            cylinder_count: finalCylinderCount,
            mileage: finalMileage,
            year: finalYear,
        };

        setIsLoading(true);
        try {
            const result = await submitPrediction(predictionData);
            onPredictionSuccess(result, predictionData);
        } catch (error: any) {
            console.error("Prediction submission failed", error);
            const errorMessage = error.response?.data?.detail || error.message || "Prediction failed. Please try again.";
            onPredictionError(errorMessage);
        }
        setIsLoading(false);
    };

    if (isFetchingOptions) {
        return (
            <div className={`${styles.formContainer} animate-scale-in`}>
                <LoadingSpinner
                    variant="ring"
                    color="primary"
                    size="large"
                    message="Loading form options..."
                />
            </div>
        );
    }

    return (
        <div className={`${styles.formContainer} animate-scale-in hover-lift`}>
            <h3 className="animate-slide-in-top">🎯 Predict Car Price</h3>

            <CarImage
                carDetails={carImageDetails}
                showFallbackInfo={false}
                alt={`${year} ${make} ${model}`}
            />

            <form onSubmit={handleSubmit}>
                <div className={styles.formGroup}>
                    <label htmlFor="make">Make:</label>
                    <select id="make" value={make} onChange={(e) => setMake(e.target.value)} required className={styles.formSelect} disabled={isFetchingOptions}>
                        <option value="">Select Make</option>
                        {availableMakes.map((m) => (<option key={m} value={m}>{m}</option>))}
                    </select>
                </div>

                <div className={styles.formGroup}>
                    <label htmlFor="model">Model:</label>
                    <select id="model" value={model} onChange={(e) => setModel(e.target.value)} required className={styles.formSelect} disabled={!make || availableModels.length === 0}>
                        <option value="">Select Model</option>
                        {availableModels.map((m) => (<option key={m} value={m}>{m}</option>))}
                    </select>
                </div>

                <div className={styles.formGroup}>
                    <label htmlFor="cylinderCount">Number of Cylinders:</label>
                    <select id="cylinderCount" value={cylinderCount} onChange={(e) => setCylinderCount(e.target.value === '' ? '' : Number(e.target.value))} required className={styles.formSelect} disabled={isFetchingOptions}>
                        <option value="">Select Cylinders</option>
                        {availableCylinderCounts.map((c) => (<option key={c} value={c}>{c}</option>))}
                    </select>
                </div>

                <div className={styles.formGroup}>
                    <label htmlFor="mileage">Mileage:</label>
                    <input
                        type="number"
                        id="mileage"
                        value={mileage}
                        onChange={(e) => setMileage(e.target.value === '' ? '' : Number(e.target.value))}
                        required
                        min="0"
                        placeholder="e.g., 50000"
                        className={styles.formInput}
                    />
                </div>

                <div className={styles.formGroup}>
                    <label htmlFor="year">Year (Manufacturing):</label>
                    <input
                        type="number"
                        id="year"
                        value={year}
                        onChange={(e) => setYear(e.target.value === '' ? '' : Number(e.target.value))}
                        required
                        min="1900"
                        max={new Date().getFullYear() + 1}
                        placeholder="e.g., 2018"
                        className={styles.formInput}
                    />
                </div>

                <button type="submit" disabled={isLoading || isFetchingOptions} className={`${styles.submitButton} hover-glow shimmer`}>
                    {isLoading ? (
                        <span style={{ display: 'flex', alignItems: 'center', gap: '12px', justifyContent: 'center' }}>
                            <LoadingSpinner variant="ring" color="secondary" size="small" message="" />
                            Predicting...
                        </span>
                    ) : (
                        '🚀 Predict Price'
                    )}
                </button>
            </form>
        </div>
    );
};

export default PredictorForm;