import React, { useState, useEffect, useCallback } from 'react';
import Plot from 'react-plotly.js';
import { getPriceDistribution } from '../services/apiService';
import { PriceDistributionParams, PriceDistributionDataPoint, DropdownOptions } from '../interfaces';
import styles from './PredictorForm.module.css'; // Import shared styles

interface PriceDistributionChartProps {
    dropdownOptions: DropdownOptions | null; // Pass from HomePage or fetch here if needed
}

const PriceDistributionChart: React.FC<PriceDistributionChartProps> = ({ dropdownOptions }) => {
    const [chartData, setChartData] = useState<PriceDistributionDataPoint[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    // Filter states
    const [selectedMake, setSelectedMake] = useState<string>('');
    const [yearMin, setYearMin] = useState<string>('');
    const [yearMax, setYearMax] = useState<string>('');
    const [selectedBodyType, setSelectedBodyType] = useState<string>('');

    // TODO: Populate body types dynamically if possible, or use a fixed list
    const bodyTypes = ['SUV', 'Sedan', 'Truck', 'Hatchback', 'Coupe', 'Minivan', 'Convertible', 'Wagon'];

    const fetchChartData = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        const params: PriceDistributionParams = {};
        if (selectedMake) params.make_name = selectedMake;
        if (yearMin) params.year_min = parseInt(yearMin, 10);
        if (yearMax) params.year_max = parseInt(yearMax, 10);
        if (selectedBodyType) params.body_type = selectedBodyType;

        try {
            const response = await getPriceDistribution(params);
            setChartData(response.data);
        } catch (err) {
            console.error("Failed to fetch price distribution data", err);
            setError("Could not load chart data.");
            setChartData([]); // Clear data on error
        }
        setIsLoading(false);
    }, [selectedMake, yearMin, yearMax, selectedBodyType]);

    useEffect(() => {
        fetchChartData();
    }, [fetchChartData]); // Re-fetch when filter params change via useCallback dependency

    // Prepare data for Plotly bar chart
    const plotlyData: Plotly.Data[] = [
        {
            x: chartData.map(d => `$${(d.price_bin_start / 1000).toFixed(0)}k-$${(d.price_bin_end / 1000).toFixed(0)}k`),
            y: chartData.map(d => d.count),
            type: 'bar',
            marker: {
                color: chartData.map((_, i) => `rgba(59, 130, 246, ${0.6 + (i % 3) * 0.15})`),
                line: { color: '#2563eb', width: 1 }
            },
            hovertemplate: '<b>%{x}</b><br>Count: %{y}<extra></extra>',
        },
    ];

    const layout: Partial<Plotly.Layout> = {
        title: {
            text: 'Interactive Car Price Distribution',
            font: {
                family: 'Poppins, sans-serif',
                size: 18,
                color: '#1e293b'
            }
        },
        xaxis: {
            title: {
                text: 'Price Range',
                font: { family: 'Inter, sans-serif', size: 14, color: '#475569' }
            },
            tickfont: { family: 'Inter, sans-serif', size: 12, color: '#64748b' }
        },
        yaxis: {
            title: {
                text: 'Number of Cars',
                font: { family: 'Inter, sans-serif', size: 14, color: '#475569' }
            },
            tickfont: { family: 'Inter, sans-serif', size: 12, color: '#64748b' }
        },
        bargap: 0.05,
        plot_bgcolor: 'rgba(255, 255, 255, 0.8)',
        paper_bgcolor: 'rgba(255, 255, 255, 0.9)',
        margin: { t: 60, r: 40, b: 60, l: 60 }
    };

    return (
        <div>
            <h4>Filter Options:</h4>
            <div className={styles.filterContainer}>
                <div className={styles.filterGroup}>
                    <label htmlFor="makeFilter">Make:</label>
                    <select id="makeFilter" value={selectedMake} onChange={e => setSelectedMake(e.target.value)} className={styles.formSelect}>
                        <option value="">All Makes</option>
                        {dropdownOptions?.make_model_map && Object.keys(dropdownOptions.make_model_map).sort().map(make => (
                            <option key={make} value={make}>{make}</option>
                        ))}
                    </select>
                </div>
                <div className={styles.filterGroup}>
                    <label htmlFor="yearMinFilter">Min Year:</label>
                    <input type="number" id="yearMinFilter" value={yearMin} onChange={e => setYearMin(e.target.value)} placeholder="e.g., 2010" className={styles.formInput}/>
                </div>
                <div className={styles.filterGroup}>
                    <label htmlFor="yearMaxFilter">Max Year:</label>
                    <input type="number" id="yearMaxFilter" value={yearMax} onChange={e => setYearMax(e.target.value)} placeholder="e.g., 2020" className={styles.formInput}/>
                </div>
                <div className={styles.filterGroup}>
                    <label htmlFor="bodyTypeFilter">Body Type:</label>
                    <select id="bodyTypeFilter" value={selectedBodyType} onChange={e => setSelectedBodyType(e.target.value)} className={styles.formSelect}>
                        <option value="">All Body Types</option>
                        {bodyTypes.map(bt => (
                            <option key={bt} value={bt}>{bt}</option>
                        ))}
                    </select>
                </div>
            </div>

            {isLoading && <p className={styles.loadingMessage}>Loading chart data...</p>}
            {error && <p className={styles.errorMessage}>{error}</p>}
            {!isLoading && !error && chartData.length === 0 && <p className={styles.noDataMessage}>No data available for the selected filters.</p>}
            {!isLoading && !error && chartData.length > 0 && (
                <div style={{
                    background: 'rgba(255, 255, 255, 0.9)',
                    borderRadius: '16px',
                    padding: '20px',
                    boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    backdropFilter: 'blur(10px)'
                }}>
                    <Plot
                        data={plotlyData}
                        layout={layout}
                        style={{ width: '100%', height: '450px' }}
                        config={{
                            displayModeBar: true,
                            displaylogo: false,
                            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d']
                        }}
                    />
                </div>
            )}
        </div>
    );
};

export default PriceDistributionChart;