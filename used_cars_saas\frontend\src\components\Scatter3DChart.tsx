import React, { useState, useEffect, useCallback } from 'react';
import Plot from 'react-plotly.js';
import { getScatter3DData } from '../services/apiService';
import { Scatter3DParams, Scatter3DDataPoint, Scatter3DResponse, DropdownOptions } from '../interfaces';
import styles from './PredictorForm.module.css';

interface Scatter3DChartProps {
    dropdownOptions: DropdownOptions | null;
}

const Scatter3DChart: React.FC<Scatter3DChartProps> = ({ dropdownOptions }) => {
    const [chartData, setChartData] = useState<Scatter3DDataPoint[]>([]);
    const [layoutOptions, setLayoutOptions] = useState<Partial<Plotly.Layout>>({});
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    // Filter states
    const [selectedMake, setSelectedMake] = useState<string>('');
    const [selectedBodyType, setSelectedBodyType] = useState<string>('');
    const [sampleSize, setSampleSize] = useState<number>(500);
    
    const bodyTypes = ['SUV', 'Sedan', 'Truck', 'Hatchback', 'Coupe', 'Minivan', 'Convertible', 'Wagon']; // Same as PriceDistChart

    const fetchChartData = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        const params: Scatter3DParams = { sample_size: sampleSize };
        if (selectedMake) params.make_name = selectedMake;
        if (selectedBodyType) params.body_type = selectedBodyType;

        try {
            const response = await getScatter3DData(params);
            setChartData(response.data);
            setLayoutOptions({
                title: { text: '3D Scatter: Price vs Mileage vs Car Age' },
                scene: {
                    xaxis: { title: { text: response.x_axis_label } },
                    yaxis: { title: { text: response.y_axis_label } },
                    zaxis: { title: { text: response.z_axis_label } },
                },
                margin: { l: 0, r: 0, b: 0, t: 50 } // Adjust margins for better title visibility
            });
        } catch (err) {
            console.error("Failed to fetch 3D scatter data", err);
            setError("Could not load 3D scatter chart data.");
            setChartData([]);
        }
        setIsLoading(false);
    }, [selectedMake, selectedBodyType, sampleSize]);

    useEffect(() => {
        fetchChartData();
    }, [fetchChartData]);

    const plotlyData: Plotly.Data[] = [
        {
            x: chartData.map(d => d.mileage),
            y: chartData.map(d => d.car_age),
            z: chartData.map(d => d.price),
            mode: 'markers',
            type: 'scatter3d',
            text: chartData.map(d => `${d.make_name || 'N/A'} - ${d.body_type || 'N/A'}`), // Hover text
            marker: {
                size: 5,
                // Example: color by make_name if data provides it, otherwise a default color
                // This would require a mapping from make_name to a color.
                // For simplicity, using a single color for now or letting Plotly auto-color by trace.
                color: chartData.map(d => d.price), // Color by price for a heatmap effect
                colorscale: 'Viridis', 
                opacity: 0.7
            },
        },
    ];
    
    return (
        <div>
            <h4>Filter Options:</h4>
            <div className={styles.filterContainer}>
                <div className={styles.filterGroup}>
                    <label htmlFor="scatterMakeFilter">Make:</label>
                    <select id="scatterMakeFilter" value={selectedMake} onChange={e => setSelectedMake(e.target.value)} className={styles.formSelect}>
                        <option value="">All Makes</option>
                        {dropdownOptions?.make_model_map && Object.keys(dropdownOptions.make_model_map).sort().map(make => (
                            <option key={make} value={make}>{make}</option>
                        ))}
                    </select>
                </div>
                <div className={styles.filterGroup}>
                    <label htmlFor="scatterBodyTypeFilter">Body Type:</label>
                    <select id="scatterBodyTypeFilter" value={selectedBodyType} onChange={e => setSelectedBodyType(e.target.value)} className={styles.formSelect}>
                        <option value="">All Body Types</option>
                        {bodyTypes.map(bt => (
                            <option key={bt} value={bt}>{bt}</option>
                        ))}
                    </select>
                </div>
                <div className={styles.filterGroup}>
                    <label htmlFor="sampleSize">Sample Size ({sampleSize}):</label>
                    <input 
                        type="range" 
                        id="sampleSize" 
                        min="100" 
                        max="2000" 
                        step="100" 
                        value={sampleSize} 
                        onChange={e => setSampleSize(Number(e.target.value))} 
                    />
                </div>
            </div>

            {isLoading && <p className={styles.loadingMessage}>Loading chart data...</p>}
            {error && <p className={styles.errorMessage}>{error}</p>}
            {!isLoading && !error && chartData.length === 0 && <p className={styles.noDataMessage}>No data available for the selected filters.</p>}
            {!isLoading && !error && chartData.length > 0 && (
                <Plot data={plotlyData} layout={layoutOptions} style={{ width: '100%', height: '500px' }} />
            )}
        </div>
    );
};

export default Scatter3DChart; 