// Corresponds to backend api.schemas.PredictionInput
export interface PredictionInput {
    make_name: string;
    model_name: string;
    cylinder_count: number;
    mileage: number;
    year: number;
}

// Corresponds to backend api.schemas.PredictionOutput
export interface PredictionOutput {
    predicted_price: number;
}

// Corresponds to backend api.schemas.DropdownOptionsResponse
export interface DropdownOptions {
    make_model_map: { [key: string]: string[] };
    cylinder_counts: number[];
}

// Visualization Data Structures
export interface PriceDistributionParams {
    make_name?: string;
    year_min?: number;
    year_max?: number;
    body_type?: string;
}

export interface PriceDistributionDataPoint {
    price_bin_start: number;
    price_bin_end: number;
    count: number;
}

export interface PriceDistributionResponse {
    data: PriceDistributionDataPoint[];
    filters_applied: PriceDistributionParams;
}

// 3D Scatter Plot Interfaces
export interface Scatter3DParams {
    make_name?: string;
    body_type?: string;
    sample_size?: number;
}

export interface Scatter3DDataPoint {
    price: number;
    mileage: number;
    car_age: number;
    make_name?: string;
    body_type?: string;
}

export interface Scatter3DResponse {
    data: Scatter3DDataPoint[];
    filters_applied: Scatter3DParams;
    x_axis_label: string;
    y_axis_label: string;
    z_axis_label: string;
}

// Feature Importance Interfaces
export interface FeatureImportanceDataPoint {
    feature_name: string;
    importance: number;
}

export interface FeatureImportanceResponse {
    data: FeatureImportanceDataPoint[];
    model_type: string;
    top_n?: number;
}

// Individual Prediction Explanation interfaces
export interface ShapValueDataPoint {
    feature_name: string;
    feature_value: string;
    shap_value: number;
    impact_description: string;
}

export interface PredictionExplanationResponse {
    prediction_input: PredictionInput;
    predicted_price: number;
    base_price: number;
    shap_contributions: ShapValueDataPoint[];
    confidence_interval?: {
        lower: number;
        upper: number;
    };
    total_shap_sum: number;
}

// Added for car image fetching
export interface CarImageDetails {
    make: string;
    model: string;
    year: number;
    angle?: string;
    paintId?: string;
    zoomType?: 'fullscreen' | 'center';
    bodyType?: string; // For fallback placeholder selection
}