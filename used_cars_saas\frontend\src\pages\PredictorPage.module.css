.pageContainer {
    display: flex;
    flex-direction: row;
    padding: var(--space-6);
    gap: var(--space-8);
    align-items: flex-start;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 200px);
}

.formSection {
    flex: 0 0 520px;
    position: relative;
}

.imageAndResultSection {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
    min-height: 600px;
}

.resultContainer {
    flex-grow: 1;
    background: rgba(30, 30, 35, 0.95);
    backdrop-filter: blur(10px);
    padding: var(--space-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    min-height: 300px;
}

.resultContainer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--success-500) 0%, var(--success-600) 50%, var(--success-500) 100%);
}

.resultContainer p {
    margin: 0 0 var(--space-3) 0;
    font-size: var(--text-xl);
    color: var(--secondary-300);
    font-weight: 500;
    font-family: var(--font-family-heading);
}

.predictedPriceValue {
    font-size: var(--text-5xl);
    font-weight: 800;
    color: var(--success-600);
    margin: var(--space-2) 0;
    font-family: var(--font-family-heading);
    background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
    position: relative;
}

.predictedPriceValue::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--success-500) 0%, var(--success-600) 100%);
    border-radius: var(--radius-sm);
}

.errorMessage {
    font-size: var(--text-lg);
    color: var(--error-700);
    font-weight: 600;
    background: var(--error-50);
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--error-200);
    margin: var(--space-4) 0;
}

.noPredictionMessage {
    font-size: var(--text-lg);
    color: var(--secondary-500);
    font-weight: 400;
    font-style: italic;
}

/* Feature Importance Display Styles */
.featureImportanceContainer {
    background: rgba(30, 30, 35, 0.9);
    backdrop-filter: blur(10px);
    padding: var(--space-6);
    border-radius: var(--radius-2xl);
    margin-top: var(--space-6);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.featureImportanceContainer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--warning-500) 0%, var(--warning-600) 50%, var(--warning-500) 100%);
}

.featureImportanceContainer h4 {
    margin-top: 0;
    margin-bottom: var(--space-6);
    color: var(--secondary-200);
    text-align: center;
    font-size: var(--text-xl);
    font-weight: 600;
    font-family: var(--font-family-heading);
}

.importanceList {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.importanceItem {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-3);
    font-size: var(--text-sm);
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    transition: background-color var(--transition-fast);
}

.importanceItem:hover {
    background: rgba(255, 255, 255, 0.1);
}

.featureName {
    flex: 0 0 160px;
    text-align: right;
    padding-right: var(--space-3);
    color: var(--secondary-300);
    text-transform: capitalize;
    font-weight: 500;
    font-size: var(--text-xs);
}

.importanceBarContainer {
    flex-grow: 1;
    background: var(--secondary-800);
    border-radius: var(--radius-md);
    height: 20px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
}

.importanceBar {
    height: 100%;
    border-radius: var(--radius-md);
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.importanceBar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.importanceValue {
    flex: 0 0 60px;
    text-align: left;
    padding-left: var(--space-3);
    font-weight: 600;
    color: var(--secondary-200);
    font-size: var(--text-xs);
}

.importanceNote {
    font-size: var(--text-xs);
    color: var(--secondary-400);
    text-align: center;
    margin-top: var(--space-4);
    font-style: italic;
    padding: var(--space-3);
    background: var(--secondary-900);
    border-radius: var(--radius-lg);
    border: 1px solid var(--secondary-700);
}

.loadingMessage {
    text-align: center;
    margin-top: var(--space-4);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    color: var(--primary-700);
    background: var(--primary-50);
    border: 1px solid var(--primary-200);
    font-size: var(--text-sm);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-8);
  background: rgba(30, 30, 35, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: var(--space-6);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .pageContainer {
        flex-direction: column;
        gap: var(--space-6);
        padding: var(--space-4);
    }

    .formSection {
        flex: none;
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
    }

    .imageAndResultSection {
        width: 100%;
        min-height: auto;
    }
}

@media (max-width: 768px) {
    .pageContainer {
        padding: var(--space-3);
        gap: var(--space-4);
    }

    .resultContainer {
        padding: var(--space-6);
        min-height: 250px;
    }

    .predictedPriceValue {
        font-size: var(--text-4xl);
    }

    .featureImportanceContainer {
        padding: var(--space-4);
    }

    .featureName {
        flex: 0 0 120px;
        font-size: var(--text-xs);
    }

    .importanceValue {
        flex: 0 0 50px;
    }
}

@media (max-width: 480px) {
    .pageContainer {
        padding: var(--space-2);
    }

    .resultContainer {
        padding: var(--space-4);
    }

    .predictedPriceValue {
        font-size: var(--text-3xl);
    }

    .importanceItem {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-2);
        padding: var(--space-3);
        background: rgba(255, 255, 255, 0.5);
        border-radius: var(--radius-lg);
        margin-bottom: var(--space-2);
    }

    .featureName {
        flex: none;
        text-align: left;
        padding-right: 0;
        font-weight: 600;
    }

    .importanceBarContainer {
        height: 16px;
    }

    .importanceValue {
        flex: none;
        text-align: center;
        padding-left: 0;
        font-weight: 700;
    }
}