import React, { useState } from 'react';
import PredictorForm from '../components/PredictorForm';
import PredictionExplanation from '../components/PredictionExplanation';
import LoadingSpinner from '../components/LoadingSpinner';
import { PredictionOutput, PredictionInput, PredictionExplanationResponse } from '../interfaces';
import { getPredictionExplanation } from '../services/apiService';
import styles from './PredictorPage.module.css'; // Import CSS Module



const PredictorPage: React.FC = () => {
  const [predictionResult, setPredictionResult] = useState<PredictionOutput | null>(null);
  const [predictionError, setPredictionError] = useState<string>('');
  const [predictionExplanation, setPredictionExplanation] = useState<PredictionExplanationResponse | null>(null);
  const [isLoadingExplanation, setIsLoadingExplanation] = useState<boolean>(false);


  const handleSuccess = async (data: PredictionOutput, inputData: PredictionInput) => {
    setPredictionResult(data);
    setPredictionError('');
    setIsLoadingExplanation(true);

    try {
        // Fetch personalized prediction explanation using SHAP values
        const explanationData = await getPredictionExplanation(inputData);
        setPredictionExplanation(explanationData);
    } catch (err) {
        console.error("Failed to fetch prediction explanation", err);
        setPredictionExplanation(null); // Clear on error
    }
    setIsLoadingExplanation(false);
  };

  const handleError = (message: string) => {
    setPredictionError(message);
    setPredictionResult(null);
    setPredictionExplanation(null); // Clear explanation on error too
  };

  return (
    <div className={`${styles.pageContainer} animate-fade-in`}>
        <div className={`${styles.formSection} animate-slide-in-left`}>
            <PredictorForm
                onPredictionSuccess={handleSuccess}
                onPredictionError={handleError}
            />
        </div>
        {/* The image is now part of PredictorForm. This right section is just for the result. */}
        <div className={`${styles.imageAndResultSection} animate-slide-in-right`}>
            <div className={`${styles.resultContainer} hover-lift`}>
                {predictionError && (
                    <div className={`${styles.errorMessage} animate-scale-in`}>
                        <span style={{ fontSize: '1.5rem', marginRight: '8px' }}>⚠️</span>
                        Error: {predictionError}
                    </div>
                )}
                {predictionResult && (
                    <div className="animate-scale-in">
                        <p>💰 Predicted Price:</p>
                        <h2 className={`${styles.predictedPriceValue} animate-bounce`}>
                            ${predictionResult.predicted_price.toFixed(2)}
                        </h2>
                    </div>
                )}
                {!predictionResult && !predictionError && (
                    <div className={`${styles.noPredictionMessage} animate-pulse`}>
                        <span style={{ fontSize: '3rem', marginBottom: '16px', display: 'block' }}>🎯</span>
                        Prediction result will appear here.
                    </div>
                )}
            </div>
            {/* Display Personalized Prediction Explanation */}
            {isLoadingExplanation && (
                <div className={styles.loadingContainer}>
                    <LoadingSpinner
                        variant="ring"
                        color="warning"
                        size="medium"
                        message="Analyzing your car's specific features..."
                    />
                </div>
            )}
            {!isLoadingExplanation && predictionExplanation && (
                <div className="animate-fade-in">
                    <PredictionExplanation explanation={predictionExplanation} />
                </div>
            )}
        </div>
    </div>
  );
};

export default PredictorPage;