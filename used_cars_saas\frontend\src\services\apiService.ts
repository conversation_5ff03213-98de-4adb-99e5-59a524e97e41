import axios from 'axios';
import { PredictionInput, PredictionOutput, DropdownOptions, PriceDistributionParams, PriceDistributionResponse, Scatter3DParams, Scatter3DDataPoint, Scatter3DResponse, FeatureImportanceDataPoint, FeatureImportanceResponse, PredictionExplanationResponse } from '../interfaces';
import { getCarPlaceholderDataUrl } from '../utils/carPlaceholders';

// Assuming the backend API is running on localhost:8000 as per backend/main.py
// and the API prefix is /api/v1 as per backend/core/config.py
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api/v1';

const apiClient = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

export const getDropdownOptions = async (): Promise<DropdownOptions> => {
    try {
        const response = await apiClient.get<DropdownOptions>('/dropdown-options');
        return response.data;
    } catch (error) {
        console.error('Error fetching dropdown options:', error);
        // Return a default structure or re-throw to be handled by the component
        throw error;
    }
};

export const submitPrediction = async (data: PredictionInput): Promise<PredictionOutput> => {
    try {
        const response = await apiClient.post<PredictionOutput>('/predict', data);
        return response.data;
    } catch (error) {
        console.error('Error submitting prediction:', error);
        throw error;
    }
};

export const getPriceDistribution = async (params?: PriceDistributionParams): Promise<PriceDistributionResponse> => {
    try {
        const response = await apiClient.post<PriceDistributionResponse>('/price-distribution', params || {});
        return response.data;
    } catch (error) {
        console.error('Error fetching price distribution data:', error);
        throw error;
    }
};

export const getScatter3DData = async (params?: Scatter3DParams): Promise<Scatter3DResponse> => {
    try {
        const response = await apiClient.post<Scatter3DResponse>('/scatter-3d-data', params || { sample_size: 500 }); // Default sample size
        return response.data;
    } catch (error) {
        console.error('Error fetching 3D scatter plot data:', error);
        throw error;
    }
};

export const getFeatureImportance = async (model_type: string = 'main', top_n: number = 15): Promise<FeatureImportanceResponse> => {
    try {
        const response = await apiClient.get<FeatureImportanceResponse>('/feature-importance', {
            params: { model_type, top_n }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching feature importance data:', error);
        throw error;
    }
};

export const getPredictionExplanation = async (predictionInput: PredictionInput): Promise<PredictionExplanationResponse> => {
    try {
        const response = await apiClient.post<PredictionExplanationResponse>('/prediction-explanation', predictionInput);
        return response.data;
    } catch (error) {
        console.error('Error fetching prediction explanation:', error);
        throw error;
    }
};

// Interface for car details needed by image services
export interface CarImageDetails {
    make: string;
    model: string;
    year: number;
    angle?: string; // e.g., '23', '01', 'front'
    paintId?: string; // For specific colors, if known
    zoomType?: 'fullscreen' | 'center';
    bodyType?: string; // For fallback placeholder selection
}

// Image service configuration
const IMAGIN_CUSTOMER_KEY = 'hrjavascript-mastery'; // Demo key from dev.to article

// Car make to body type mapping for better placeholders
const MAKE_TO_BODY_TYPE: { [key: string]: string } = {
    'BMW': 'luxury-sedan',
    'Mercedes-Benz': 'luxury-sedan',
    'Audi': 'luxury-sedan',
    'Lexus': 'luxury-sedan',
    'Cadillac': 'luxury-sedan',
    'Ford': 'sedan',
    'Toyota': 'sedan',
    'Honda': 'sedan',
    'Nissan': 'sedan',
    'Chevrolet': 'sedan',
    'Hyundai': 'sedan',
    'Kia': 'sedan',
    'Volkswagen': 'sedan',
    'Subaru': 'suv',
    'Jeep': 'suv',
    'Land Rover': 'suv',
    'Porsche': 'sports',
    'Ferrari': 'sports',
    'Lamborghini': 'sports',
    'Maserati': 'sports',
    'Dodge': 'muscle',
    'Chrysler': 'sedan',
    'Ram': 'truck',
    'GMC': 'truck',
    'Chevrolet Silverado': 'truck',
    'Ford F-150': 'truck'
};

// Placeholder image URLs (Unsplash as secondary fallback, SVG as final fallback)
const PLACEHOLDER_IMAGES = {
    'sedan': 'https://images.unsplash.com/photo-1549924231-f129b911e442?w=400&h=300&fit=crop&crop=center',
    'suv': 'https://images.unsplash.com/photo-1519641471654-76ce0107ad1b?w=400&h=300&fit=crop&crop=center',
    'truck': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center',
    'luxury-sedan': 'https://images.unsplash.com/photo-1563720223185-11003d516935?w=400&h=300&fit=crop&crop=center',
    'sports': 'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=400&h=300&fit=crop&crop=center',
    'muscle': 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=400&h=300&fit=crop&crop=center',
    'hatchback': 'https://images.unsplash.com/photo-1502877338535-766e1452684a?w=400&h=300&fit=crop&crop=center',
    'coupe': 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=400&h=300&fit=crop&crop=center',
    'convertible': 'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=400&h=300&fit=crop&crop=center',
    'wagon': 'https://images.unsplash.com/photo-1549924231-f129b911e442?w=400&h=300&fit=crop&crop=center',
    'minivan': 'https://images.unsplash.com/photo-1570125909232-eb263c188f7e?w=400&h=300&fit=crop&crop=center',
    'default': 'https://images.unsplash.com/photo-1549924231-f129b911e442?w=400&h=300&fit=crop&crop=center'
};

// Primary image service - Imagin Studio
const getImaginStudioUrl = (details: CarImageDetails): string => {
    const { make, model, year, angle = '01', zoomType = 'fullscreen' } = details;

    const url = new URL("https://cdn.imagin.studio/getimage");
    url.searchParams.append("customer", IMAGIN_CUSTOMER_KEY);
    url.searchParams.append("make", make);
    url.searchParams.append("modelFamily", model.split(' ')[0]);
    url.searchParams.append("modelYear", String(year));
    url.searchParams.append("angle", angle);
    url.searchParams.append("zoomType", zoomType);

    return url.toString();
};

// Secondary image service - CarQuery API (alternative)
const getCarQueryUrl = (details: CarImageDetails): string => {
    const { make, model, year } = details;
    // This is a hypothetical alternative - you'd need to sign up for actual service
    return `https://www.carqueryapi.com/api/0.3/?callback=?&cmd=getModel&make=${encodeURIComponent(make)}&model=${encodeURIComponent(model)}&year=${year}`;
};

// Get appropriate placeholder based on car details
const getPlaceholderImage = (details: CarImageDetails): string => {
    const { make, model, bodyType } = details;

    // First try to determine body type from the model name
    const modelLower = model.toLowerCase();
    let determinedBodyType = bodyType;

    if (!determinedBodyType) {
        if (modelLower.includes('suv') || modelLower.includes('explorer') || modelLower.includes('tahoe') ||
            modelLower.includes('suburban') || modelLower.includes('escalade') || modelLower.includes('pilot')) {
            determinedBodyType = 'suv';
        } else if (modelLower.includes('truck') || modelLower.includes('f-150') || modelLower.includes('silverado') ||
                   modelLower.includes('ram') || modelLower.includes('sierra')) {
            determinedBodyType = 'truck';
        } else if (modelLower.includes('coupe') || modelLower.includes('mustang') || modelLower.includes('camaro') ||
                   modelLower.includes('corvette')) {
            determinedBodyType = 'sports';
        } else if (modelLower.includes('hatchback') || modelLower.includes('golf') || modelLower.includes('civic hatchback')) {
            determinedBodyType = 'hatchback';
        } else {
            // Use make-based fallback
            determinedBodyType = MAKE_TO_BODY_TYPE[make] || 'sedan';
        }
    }

    return PLACEHOLDER_IMAGES[determinedBodyType as keyof typeof PLACEHOLDER_IMAGES] || PLACEHOLDER_IMAGES.default;
};

// Main function with fallback chain
export const getCarImageUrl = (details: CarImageDetails): string => {
    // Always return the primary URL first - fallbacks will be handled in the component
    return getImaginStudioUrl(details);
};

// Function to get all possible image URLs in order of preference
export const getCarImageUrls = (details: CarImageDetails): string[] => {
    const bodyType = getBodyTypeFromDetails(details);

    return [
        getImaginStudioUrl(details),
        // Add more alternative APIs here when available
        getPlaceholderImage(details),
        // Final fallback - local SVG that will always work
        getCarPlaceholderDataUrl(bodyType)
    ];
};

// Helper function to determine body type from car details
const getBodyTypeFromDetails = (details: CarImageDetails): string => {
    const { make, model, bodyType } = details;

    if (bodyType) return bodyType;

    // Try to determine from model name
    const modelLower = model.toLowerCase();
    if (modelLower.includes('suv') || modelLower.includes('explorer') || modelLower.includes('tahoe')) {
        return 'suv';
    } else if (modelLower.includes('truck') || modelLower.includes('f-150') || modelLower.includes('silverado')) {
        return 'truck';
    } else if (modelLower.includes('coupe') || modelLower.includes('mustang') || modelLower.includes('camaro')) {
        return 'sports';
    }

    // Use make-based fallback
    return MAKE_TO_BODY_TYPE[make] || 'sedan';
};

// Function to get just the placeholder (guaranteed to work)
export const getCarPlaceholderImage = (details: CarImageDetails): string => {
    const bodyType = getBodyTypeFromDetails(details);
    return getCarPlaceholderDataUrl(bodyType);
};

// Model management functions
export interface ModelStatus {
    main_model_loaded: boolean;
    error_model_loaded: boolean;
    can_predict: boolean;
}

export const getModelStatus = async (): Promise<ModelStatus> => {
    try {
        const response = await apiClient.get<ModelStatus>('/model-status');
        return response.data;
    } catch (error) {
        console.error('Error fetching model status:', error);
        throw error;
    }
};

export const loadMainModel = async (): Promise<any> => {
    try {
        const response = await apiClient.post('/load-main-model');
        return response.data;
    } catch (error) {
        console.error('Error loading main model:', error);
        throw error;
    }
};

export const loadErrorModel = async (): Promise<any> => {
    try {
        const response = await apiClient.post('/load-error-model');
        return response.data;
    } catch (error) {
        console.error('Error loading error model:', error);
        throw error;
    }
};

export const loadBothModels = async (): Promise<any> => {
    try {
        const response = await apiClient.post('/load-both-models');
        return response.data;
    } catch (error) {
        console.error('Error loading both models:', error);
        throw error;
    }
};