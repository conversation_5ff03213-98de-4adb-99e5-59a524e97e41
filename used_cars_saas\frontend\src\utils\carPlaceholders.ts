// SVG-based car placeholders that will always work
export const generateCarPlaceholderSVG = (type: string, color: string = '#3B82F6'): string => {
  const svgTemplates = {
    sedan: `
      <svg viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="carGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${adjustColor(color, -20)};stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="400" height="300" fill="#f8fafc"/>
        <!-- Car body -->
        <path d="M50 180 L80 140 L120 120 L280 120 L320 140 L350 180 L350 220 L320 240 L80 240 L50 220 Z" 
              fill="url(#carGradient)" stroke="${adjustColor(color, -30)}" stroke-width="2"/>
        <!-- Windows -->
        <path d="M90 140 L110 130 L290 130 L310 140 L310 160 L90 160 Z" 
              fill="#e2e8f0" stroke="#94a3b8" stroke-width="1"/>
        <!-- Wheels -->
        <circle cx="120" cy="220" r="25" fill="#374151" stroke="#1f2937" stroke-width="2"/>
        <circle cx="280" cy="220" r="25" fill="#374151" stroke="#1f2937" stroke-width="2"/>
        <circle cx="120" cy="220" r="15" fill="#6b7280"/>
        <circle cx="280" cy="220" r="15" fill="#6b7280"/>
        <!-- Headlights -->
        <ellipse cx="340" cy="160" rx="8" ry="12" fill="#fbbf24"/>
        <!-- Grille -->
        <rect x="320" y="150" width="20" height="20" fill="#374151"/>
      </svg>
    `,
    suv: `
      <svg viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="suvGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${adjustColor(color, -20)};stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="400" height="300" fill="#f8fafc"/>
        <!-- Car body (taller for SUV) -->
        <path d="M50 170 L80 110 L120 90 L280 90 L320 110 L350 170 L350 220 L320 240 L80 240 L50 220 Z" 
              fill="url(#suvGradient)" stroke="${adjustColor(color, -30)}" stroke-width="2"/>
        <!-- Windows -->
        <path d="M90 110 L110 100 L290 100 L310 110 L310 150 L90 150 Z" 
              fill="#e2e8f0" stroke="#94a3b8" stroke-width="1"/>
        <!-- Wheels (larger for SUV) -->
        <circle cx="120" cy="220" r="30" fill="#374151" stroke="#1f2937" stroke-width="2"/>
        <circle cx="280" cy="220" r="30" fill="#374151" stroke="#1f2937" stroke-width="2"/>
        <circle cx="120" cy="220" r="18" fill="#6b7280"/>
        <circle cx="280" cy="220" r="18" fill="#6b7280"/>
        <!-- Headlights -->
        <ellipse cx="340" cy="140" rx="10" ry="15" fill="#fbbf24"/>
        <!-- Grille -->
        <rect x="320" y="130" width="25" height="25" fill="#374151"/>
        <!-- Roof rails -->
        <rect x="90" y="95" width="220" height="3" fill="#9ca3af"/>
      </svg>
    `,
    truck: `
      <svg viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="truckGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${adjustColor(color, -20)};stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="400" height="300" fill="#f8fafc"/>
        <!-- Truck cab -->
        <path d="M50 170 L80 110 L120 90 L200 90 L220 110 L220 170 L220 220 L190 240 L80 240 L50 220 Z" 
              fill="url(#truckGradient)" stroke="${adjustColor(color, -30)}" stroke-width="2"/>
        <!-- Truck bed -->
        <rect x="220" y="140" width="120" height="80" fill="${adjustColor(color, -10)}" 
              stroke="${adjustColor(color, -30)}" stroke-width="2"/>
        <!-- Windows -->
        <path d="M90 110 L110 100 L190 100 L200 110 L200 150 L90 150 Z" 
              fill="#e2e8f0" stroke="#94a3b8" stroke-width="1"/>
        <!-- Wheels -->
        <circle cx="120" cy="220" r="28" fill="#374151" stroke="#1f2937" stroke-width="2"/>
        <circle cx="280" cy="220" r="28" fill="#374151" stroke="#1f2937" stroke-width="2"/>
        <circle cx="120" cy="220" r="16" fill="#6b7280"/>
        <circle cx="280" cy="220" r="16" fill="#6b7280"/>
        <!-- Headlights -->
        <ellipse cx="210" cy="140" rx="8" ry="12" fill="#fbbf24"/>
        <!-- Grille -->
        <rect x="200" y="130" width="15" height="20" fill="#374151"/>
      </svg>
    `,
    sports: `
      <svg viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="sportsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${adjustColor(color, -20)};stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="400" height="300" fill="#f8fafc"/>
        <!-- Sports car body (low and sleek) -->
        <path d="M60 190 L90 150 L130 130 L270 130 L310 150 L340 190 L340 210 L310 230 L90 230 L60 210 Z" 
              fill="url(#sportsGradient)" stroke="${adjustColor(color, -30)}" stroke-width="2"/>
        <!-- Windows (smaller, more angular) -->
        <path d="M100 150 L120 140 L280 140 L300 150 L300 170 L100 170 Z" 
              fill="#e2e8f0" stroke="#94a3b8" stroke-width="1"/>
        <!-- Wheels (performance style) -->
        <circle cx="130" cy="210" r="22" fill="#374151" stroke="#1f2937" stroke-width="2"/>
        <circle cx="270" cy="210" r="22" fill="#374151" stroke="#1f2937" stroke-width="2"/>
        <circle cx="130" cy="210" r="12" fill="#6b7280"/>
        <circle cx="270" cy="210" r="12" fill="#6b7280"/>
        <!-- Headlights (sleek) -->
        <ellipse cx="325" cy="170" rx="6" ry="10" fill="#fbbf24"/>
        <!-- Air intake -->
        <rect x="310" y="165" width="15" height="10" fill="#374151"/>
        <!-- Spoiler -->
        <rect x="70" y="125" width="15" height="5" fill="${adjustColor(color, -30)}"/>
      </svg>
    `
  };

  return svgTemplates[type as keyof typeof svgTemplates] || svgTemplates.sedan;
};

// Helper function to adjust color brightness
const adjustColor = (color: string, amount: number): string => {
  // Simple color adjustment - in a real app you might want a more sophisticated color library
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const r = Math.max(0, Math.min(255, (num >> 16) + amount));
  const g = Math.max(0, Math.min(255, ((num >> 8) & 0x00FF) + amount));
  const b = Math.max(0, Math.min(255, (num & 0x0000FF) + amount));
  return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
};

// Convert SVG to data URL
export const svgToDataUrl = (svgString: string): string => {
  const encoded = encodeURIComponent(svgString);
  return `data:image/svg+xml,${encoded}`;
};

// Get a placeholder image data URL for a car type
export const getCarPlaceholderDataUrl = (type: string, color?: string): string => {
  const svg = generateCarPlaceholderSVG(type, color);
  return svgToDataUrl(svg);
};
